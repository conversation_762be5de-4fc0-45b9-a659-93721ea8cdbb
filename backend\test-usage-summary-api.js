/**
 * 测试全局使用量汇总API
 * 验证新增的 /api/admin/usage/summary 接口功能
 */

// 使用Node.js内置fetch (Node 18+) 或者http模块
let fetch;
try {
  // Node 18+ 内置fetch
  fetch = globalThis.fetch;
  if (!fetch) {
    throw new Error('fetch not available');
  }
} catch (error) {
  // 回退到http模块
  const http = require('http');
  const https = require('https');
  const { URL } = require('url');

  fetch = async (url, options = {}) => {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;

      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: options.headers || {}
      };

      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            json: () => Promise.resolve(JSON.parse(data)),
            text: () => Promise.resolve(data)
          });
        });
      });

      req.on('error', reject);

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  };
}

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMDYwMzUyODI0IiwidHlwZSI6ImFjY2VzcyIsImlhdCI6MTc1Mzg2NTQzMDc1MywiZXhwIjoxNzUzODcyNjMwNzUzfQ==.C2ilnUAYZIpPCXCo4B4KMOqE7Zb4Dxp33ylQp3uVSGA=';

// 颜色输出函数
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 格式化字节数
function formatBytes(bytes) {
  if (bytes === 0) return '0 字符';
  const k = 1000;
  const sizes = ['字符', 'K字符', 'M字符'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 格式化百分比
function formatPercentage(value) {
  return `${value.toFixed(2)}%`;
}

// 测试全局使用量汇总接口
async function testUsageSummaryAPI() {
  try {
    log('blue', '\n🧪 测试全局使用量汇总API');
    log('blue', '=' .repeat(60));

    const response = await fetch(`${API_BASE_URL}/api/admin/usage/summary`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }

    const data = await response.json();
    
    log('green', '✅ API调用成功');
    console.log();

    // 显示全局汇总数据
    log('cyan', '📊 全局汇总数据:');
    const global = data.globalSummary;
    console.log(`  历史总使用量: ${formatBytes(global.totalCharsAllTime)}`);
    console.log(`  当月总使用量: ${formatBytes(global.totalCharsCurrentMonth)}`);
    console.log(`  配额已使用量: ${formatBytes(global.totalQuotaUsed)}`);
    console.log(`  配额分配总量: ${formatBytes(global.totalQuotaAllocated)}`);
    console.log(`  全局配额使用率: ${formatPercentage(global.globalQuotaUsageRate)}`);
    console.log(`  总用户数: ${global.totalUsers}`);
    console.log(`  老用户数: ${global.legacyUsers}`);
    console.log(`  新用户数: ${global.quotaUsers}`);
    console.log(`  本月活跃用户: ${global.activeUsersThisMonth}`);
    console.log(`  人均历史使用量: ${formatBytes(global.avgTotalCharsPerUser)}`);
    console.log(`  人均月度使用量: ${formatBytes(global.avgMonthlyCharsPerUser)}`);
    console.log();

    // 显示VIP类型分布
    log('cyan', '👑 VIP类型分布:');
    data.vipDistribution.forEach((vip, index) => {
      console.log(`  ${index + 1}. ${vip.vipType}:`);
      console.log(`     用户数: ${vip.userCount}`);
      console.log(`     总使用量: ${formatBytes(vip.totalChars)}`);
      console.log(`     月度使用量: ${formatBytes(vip.monthlyChars)}`);
      console.log(`     人均总使用量: ${formatBytes(vip.avgTotalChars)}`);
      console.log(`     人均月度使用量: ${formatBytes(vip.avgMonthlyChars)}`);
      console.log();
    });

    // 显示使用量趋势
    log('cyan', '📈 最近7天使用趋势:');
    data.usageTrend.forEach(trend => {
      console.log(`  ${trend.date}: ${trend.tasksCount}个任务, ${trend.activeUsers}个活跃用户`);
    });
    console.log();

    // 显示配额使用率TOP用户
    if (data.topQuotaUsers.length > 0) {
      log('cyan', '🏆 配额使用率TOP用户:');
      data.topQuotaUsers.forEach((user, index) => {
        const statusColor = user.usagePercentage > 90 ? 'red' : 
                           user.usagePercentage > 70 ? 'yellow' : 'green';
        log(statusColor, `  ${index + 1}. ${user.username} (${user.vipType})`);
        log(statusColor, `     ${formatBytes(user.usedChars)}/${formatBytes(user.quotaChars)} (${formatPercentage(user.usagePercentage)})`);
      });
      console.log();
    }

    // 显示月度重置状态
    log('cyan', '🔄 月度重置状态:');
    const reset = data.monthlyResetStatus;
    console.log(`  总用户数: ${reset.totalUsers}`);
    console.log(`  需要重置: ${reset.usersNeedReset}`);
    console.log(`  重置正常: ${reset.usersResetOk}`);
    console.log();

    // 显示元数据
    log('cyan', '📋 元数据:');
    console.log(`  生成时间: ${data.timestamp}`);
    console.log(`  生成者: ${data.generatedBy}`);
    console.log();

    log('green', '✅ 全局使用量汇总API测试完成');

  } catch (error) {
    log('red', `❌ 测试失败: ${error.message}`);
    
    if (error.message.includes('401')) {
      log('yellow', '💡 提示: 请检查ADMIN_TOKEN是否正确');
    } else if (error.message.includes('403')) {
      log('yellow', '💡 提示: 请确保使用的是管理员账户的Token');
    } else if (error.message.includes('ECONNREFUSED')) {
      log('yellow', '💡 提示: 请确保后端服务正在运行 (http://localhost:3001)');
    }
  }
}

// 测试对比现有stats接口
async function compareWithExistingStats() {
  try {
    log('blue', '\n🔍 对比现有stats接口');
    log('blue', '=' .repeat(60));

    const response = await fetch(`${API_BASE_URL}/api/admin/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    
    log('cyan', '📊 现有stats接口数据:');
    console.log(`  总用户数: ${data.users.total_users}`);
    console.log(`  活跃VIP用户: ${data.users.active_vip_users}`);
    console.log(`  7天新用户: ${data.users.new_users_7d}`);
    console.log(`  30天新用户: ${data.users.new_users_30d}`);
    console.log();
    console.log(`  总任务数: ${data.tasks.total_tasks}`);
    console.log(`  完成任务: ${data.tasks.completed_tasks}`);
    console.log(`  失败任务: ${data.tasks.failed_tasks}`);
    console.log(`  24小时任务: ${data.tasks.tasks_24h}`);
    console.log();

    log('green', '✅ 现有接口对比完成');
    log('yellow', '💡 新的usage/summary接口提供了更详细的使用量统计数据');

  } catch (error) {
    log('red', `❌ 对比测试失败: ${error.message}`);
  }
}

// 主函数
async function main() {
  log('blue', '🚀 开始测试全局使用量汇总API');
  
  if (ADMIN_TOKEN === 'your_admin_token_here') {
    log('yellow', '⚠️  请设置正确的ADMIN_TOKEN环境变量');
    log('yellow', '   示例: ADMIN_TOKEN=your_actual_token node test-usage-summary-api.js');
    return;
  }

  await testUsageSummaryAPI();
  await compareWithExistingStats();
  
  log('blue', '\n🎉 测试完成');
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    log('red', `❌ 测试运行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  testUsageSummaryAPI,
  compareWithExistingStats
};
