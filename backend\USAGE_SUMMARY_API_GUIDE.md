# 全局使用量汇总API使用指南

## 📋 概述

新增的全局使用量汇总API (`/api/admin/usage/summary`) 为管理员提供了系统级的使用量统计和分析功能，帮助管理员全面了解系统的使用情况。

## 🎯 功能特性

### ✨ 核心功能
- **全局使用量统计** - 历史总量、当月使用量、配额使用情况
- **用户分类分析** - 老用户vs新用户、活跃用户统计
- **VIP类型分布** - 按VIP类型统计使用量和用户数
- **使用趋势分析** - 最近7天的使用趋势
- **配额使用排行** - TOP10配额使用率用户
- **系统健康检查** - 月度重置状态监控

### 🔒 安全特性
- 管理员权限验证
- 详细的操作日志记录
- 安全的错误处理

## 🚀 快速开始

### 1. 基本调用

```bash
curl -X GET http://localhost:3001/api/admin/usage/summary \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 2. JavaScript调用

```javascript
const getUsageSummary = async () => {
  const response = await fetch('/api/admin/usage/summary', {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  const data = await response.json();
  return data;
};
```

### 3. 测试接口

```bash
# 运行测试脚本
node test-usage-summary-api.js

# 或设置Token后运行
ADMIN_TOKEN=your_token node test-usage-summary-api.js
```

## 📊 响应数据结构

### globalSummary（全局汇总）
```json
{
  "totalCharsAllTime": 1250000,      // 历史总使用量
  "totalCharsCurrentMonth": 85000,   // 当月使用量
  "totalQuotaUsed": 450000,          // 配额已使用
  "totalQuotaAllocated": 2400000,    // 配额总分配
  "globalQuotaUsageRate": 18.75,     // 全局配额使用率
  "totalUsers": 150,                 // 总用户数
  "legacyUsers": 45,                 // 老用户数
  "quotaUsers": 105,                 // 新用户数
  "activeUsersThisMonth": 68,        // 本月活跃用户
  "avgTotalCharsPerUser": 8333,      // 人均历史使用量
  "avgMonthlyCharsPerUser": 567      // 人均月度使用量
}
```

### vipDistribution（VIP分布）
```json
[
  {
    "vipType": "M",                  // VIP类型
    "userCount": 35,                 // 用户数量
    "totalChars": 420000,            // 总使用量
    "monthlyChars": 28000,           // 月度使用量
    "avgTotalChars": 12000,          // 平均总使用量
    "avgMonthlyChars": 800           // 平均月度使用量
  }
]
```

### topQuotaUsers（配额使用排行）
```json
[
  {
    "username": "poweruser1",        // 用户名
    "vipType": "PM",                 // VIP类型
    "quotaChars": 200000,            // 总配额
    "usedChars": 185000,             // 已使用
    "usagePercentage": 92.5          // 使用率
  }
]
```

## 🎨 使用场景

### 1. 系统监控仪表板
```javascript
// 获取关键指标
const summary = await getUsageSummary();
const {
  totalCharsCurrentMonth,
  globalQuotaUsageRate,
  activeUsersThisMonth
} = summary.globalSummary;

// 显示在仪表板上
updateDashboard({
  monthlyUsage: totalCharsCurrentMonth,
  quotaUsage: globalQuotaUsageRate,
  activeUsers: activeUsersThisMonth
});
```

### 2. 容量规划
```javascript
// 分析使用趋势
const { usageTrend, globalSummary } = await getUsageSummary();
const avgDailyTasks = usageTrend.reduce((sum, day) => sum + day.tasksCount, 0) / 7;
const projectedMonthlyUsage = avgDailyTasks * 30;

// 容量预警
if (globalSummary.globalQuotaUsageRate > 80) {
  alert('配额使用率过高，需要考虑扩容');
}
```

### 3. 用户行为分析
```javascript
// 分析VIP类型效果
const { vipDistribution } = await getUsageSummary();
const vipAnalysis = vipDistribution.map(vip => ({
  type: vip.vipType,
  avgUsage: vip.avgMonthlyChars,
  efficiency: vip.avgMonthlyChars / vip.userCount
}));
```

## ⚠️ 注意事项

### 权限要求
- 必须使用管理员Token
- 需要在环境变量中配置ADMIN_USERS

### 性能考虑
- 接口涉及大量数据聚合，建议适度调用
- 建议缓存结果，避免频繁请求
- 大数据量时可能需要几秒钟响应时间

### 数据准确性
- 月度数据基于用户的monthlyResetAt字段
- 老用户（无配额限制）的配额相关数据为0
- 实时数据可能有轻微延迟

## 🔧 故障排除

### 常见错误

**401 Unauthorized**
```
解决方案: 检查Token是否正确设置
```

**403 Forbidden**
```
解决方案: 确保使用管理员账户Token
```

**500 Internal Server Error**
```
解决方案: 检查数据库连接和数据完整性
```

### 调试技巧

1. **启用调试日志**
```bash
DEBUG=true node your-app.js
```

2. **检查数据库连接**
```bash
node test-db-pool.js
```

3. **验证管理员权限**
```bash
node test-admin-permissions.js
```

## 📈 扩展建议

### 未来可能的增强功能
- 按时间段自定义统计
- 导出功能（CSV/Excel）
- 实时数据推送
- 更细粒度的用户分组
- 成本分析和预测

### 集成建议
- 与监控系统集成（如Grafana）
- 设置自动报警阈值
- 定期生成使用报告
- 与计费系统对接

## 📚 相关文档

- [管理员API接口文档.md](./管理员API接口文档.md) - 完整API文档
- [test-usage-summary-api.js](./test-usage-summary-api.js) - 测试脚本
- [VIP系统完善说明.md](./VIP系统完善说明.md) - VIP系统说明

---

**版本**: v1.0.0  
**更新时间**: 2025-01-30  
**维护者**: 系统管理员
