#!/usr/bin/env node

const crypto = require('crypto');

const targetHash = 'lRXeSeXTgpXQ2tMM2B1PxleXirwaJwd8PYPPYOslCuU=';
const jwtSecret = 'X8k9#mP2$vL5nQ7@jR3wY6*tZ4';

console.log('🔍 使用指定JWT_SECRET进行密码哈希分析');
console.log('=' .repeat(60));
console.log('目标哈希:', targetHash);
console.log('JWT_SECRET:', jwtSecret);
console.log('');

// 密码加密函数
function bcrypt(password, secret) {
  const data = password + secret;
  const hash = crypto.createHash('sha256').update(data).digest();
  return Buffer.from(hash).toString('base64');
}

// 常见密码列表
const passwords = [
  '123456', 'password', '123456789', '12345678', '12345',
  '1234567', '1234567890', 'qwerty', 'abc123', 'password123',
  'admin', 'root', 'user', 'test', '555', '888888', '666666',
  '111111', '000000', '999999', '123123', 'admin123', 'demo',
  'guest', 'login', 'signin', 'welcome', 'hello', 'world',
  'a', 'aa', 'aaa', 'aaaa', 'aaaaa', 'aaaaaa',
  '1', '11', '111', '1111', '11111', '111111',
  'abc', 'abcd', 'abcde', 'abcdef', 'abcdefg',
  'pass', 'word', 'test123', 'user123', 'admin123'
];

console.log('🧪 开始测试常见密码...');
console.log('');

let found = false;
for (const password of passwords) {
  const computedHash = bcrypt(password, jwtSecret);
  const isMatch = computedHash === targetHash;
  
  console.log(`测试 "${password}" -> ${computedHash.substring(0, 20)}... ${isMatch ? '✅ 匹配!' : ''}`);
  
  if (isMatch) {
    console.log('');
    console.log('🎉 找到匹配密码!');
    console.log('密码:', password);
    console.log('完整哈希:', computedHash);
    console.log('');
    console.log('🔧 加密过程:');
    console.log('拼接字符串:', password + jwtSecret);
    const hash = crypto.createHash('sha256').update(password + jwtSecret).digest();
    console.log('SHA256 (hex):', hash.toString('hex'));
    console.log('Base64编码:', Buffer.from(hash).toString('base64'));
    found = true;
    break;
  }
}

if (!found) {
  console.log('');
  console.log('❌ 未在常见密码中找到匹配');
  console.log('');
  console.log('💡 建议:');
  console.log('1. 尝试更多密码组合');
  console.log('2. 检查JWT_SECRET是否完整');
  console.log('3. 确认哈希算法是否正确');
}
