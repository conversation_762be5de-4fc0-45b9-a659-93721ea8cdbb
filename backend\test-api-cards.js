#!/usr/bin/env node

/**
 * 测试修改后的卡密API接口
 * 验证用户使用量字段是否正确返回
 */

const http = require('http');
require('dotenv').config();

const API_BASE = 'http://localhost:3001';

// 模拟管理员Token（需要先登录获取真实Token）
const ADMIN_TOKEN = 'your-admin-token-here';

function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE);
    
    const requestOptions = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testCardsAPI() {
  console.log('🧪 测试修改后的卡密API接口...\n');

  try {
    // 测试1: 获取卡密列表（默认参数）
    console.log('1. 测试获取卡密列表（默认参数）');
    const response1 = await makeRequest('/api/admin/cards');
    
    if (response1.status === 200) {
      console.log('✅ 接口调用成功');
      console.log(`📊 返回数据结构:`);
      console.log(`   - 卡密数量: ${response1.data.cards?.length || 0}`);
      console.log(`   - 分页信息: ${response1.data.pagination ? '有' : '无'}`);
      
      if (response1.data.cards && response1.data.cards.length > 0) {
        const sample = response1.data.cards[0];
        console.log(`   - 示例卡密:`);
        console.log(`     * ID: ${sample.id}`);
        console.log(`     * 代码: ${sample.code?.substring(0, 8)}...`);
        console.log(`     * 状态: ${sample.status}`);
        console.log(`     * 使用者: ${sample.used_by || '未使用'}`);
        console.log(`     * 用户使用量: ${sample.userUsage ? '有数据' : '无数据'}`);
        
        if (sample.userUsage) {
          console.log(`       - 总字符数: ${sample.userUsage.totalChars}`);
          console.log(`       - 月度字符数: ${sample.userUsage.monthlyChars}`);
        }
      }
    } else {
      console.log(`❌ 接口调用失败: ${response1.status}`);
      console.log(`错误信息: ${JSON.stringify(response1.data, null, 2)}`);
    }

    // 测试2: 获取已使用的卡密
    console.log('\n2. 测试获取已使用的卡密');
    const response2 = await makeRequest('/api/admin/cards?status=used&limit=5');
    
    if (response2.status === 200) {
      console.log('✅ 已使用卡密查询成功');
      const usedCards = response2.data.cards?.filter(card => card.status === 'used') || [];
      console.log(`📊 已使用卡密数量: ${usedCards.length}`);
      
      usedCards.forEach((card, index) => {
        console.log(`   卡密 ${index + 1}: ${card.code?.substring(0, 8)}... -> ${card.used_by} (使用量: ${card.userUsage ? '有' : '无'})`);
      });
    } else {
      console.log(`❌ 已使用卡密查询失败: ${response2.status}`);
    }

    // 测试3: 获取未使用的卡密
    console.log('\n3. 测试获取未使用的卡密');
    const response3 = await makeRequest('/api/admin/cards?status=unused&limit=3');
    
    if (response3.status === 200) {
      console.log('✅ 未使用卡密查询成功');
      const unusedCards = response3.data.cards?.filter(card => card.status === 'unused') || [];
      console.log(`📊 未使用卡密数量: ${unusedCards.length}`);
      
      unusedCards.forEach((card, index) => {
        console.log(`   卡密 ${index + 1}: ${card.code?.substring(0, 8)}... (使用量: ${card.userUsage ? '有' : '无'})`);
      });
    } else {
      console.log(`❌ 未使用卡密查询失败: ${response3.status}`);
    }

    console.log('\n🎉 API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  console.log('⚠️  注意: 请确保服务器正在运行，并且已设置有效的管理员Token');
  console.log('💡 提示: 如果没有Token，请先运行登录接口获取Token\n');
  
  // 检查是否设置了真实的Token
  if (ADMIN_TOKEN === 'your-admin-token-here') {
    console.log('❌ 请先设置真实的管理员Token');
    console.log('💡 获取Token的方法:');
    console.log('   1. 使用管理员账号登录: POST /api/auth/login');
    console.log('   2. 复制返回的token');
    console.log('   3. 修改此文件中的ADMIN_TOKEN变量\n');
  } else {
    testCardsAPI();
  }
}

module.exports = { testCardsAPI };
