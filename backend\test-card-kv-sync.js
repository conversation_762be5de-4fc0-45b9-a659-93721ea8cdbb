/**
 * 测试卡密KV同步功能
 * 验证生成的卡密是否能正确同步到Cloudflare KV
 */

require('dotenv').config();

// 测试配置
const TEST_CONFIG = {
  API_BASE: 'http://localhost:3001',
  API_SECRET_TOKEN: process.env.API_SECRET_TOKEN,
  CF_ACCOUNT_ID: process.env.CF_ACCOUNT_ID,
  CF_API_TOKEN: process.env.CF_API_TOKEN,
  KV_NAMESPACE_ID: '69d6e32b35dd4a0bb996584ebf3f5b27'
};

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 生成测试卡密
async function generateTestCard() {
  try {
    log('blue', '🔄 正在生成测试卡密...');
    
    const response = await fetch(`${TEST_CONFIG.API_BASE}/api/admin/cards/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_CONFIG.API_SECRET_TOKEN}`
      },
      body: JSON.stringify({
        packageType: 'M',
        quantity: 1
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success || result.cards.length === 0) {
      throw new Error('卡密生成失败');
    }

    const card = result.cards[0];
    log('green', `✅ 卡密生成成功: ${card.code}`);
    return card;
    
  } catch (error) {
    log('red', `❌ 卡密生成失败: ${error.message}`);
    throw error;
  }
}

// 检查KV中的卡密数据
async function checkCardInKV(cardCode) {
  try {
    log('blue', `🔍 检查KV中的卡密: ${cardCode}`);
    
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${TEST_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${TEST_CONFIG.KV_NAMESPACE_ID}/values/card:${cardCode}`,
      {
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.CF_API_TOKEN}`
        }
      }
    );

    if (response.status === 404) {
      log('red', '❌ 卡密在KV中不存在');
      return null;
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const kvData = await response.json();
    log('green', '✅ 卡密在KV中存在');
    log('cyan', `📋 KV数据: ${JSON.stringify(kvData, null, 2)}`);
    return kvData;
    
  } catch (error) {
    log('red', `❌ 检查KV失败: ${error.message}`);
    throw error;
  }
}

// 验证数据格式
function validateKVData(kvData, expectedCard) {
  log('blue', '🔍 验证KV数据格式...');
  
  const errors = [];
  
  // 检查必需字段
  const requiredFields = ['t', 's', 'c', 'u', 'a', 'chars', 'days', 'price'];
  for (const field of requiredFields) {
    if (!(field in kvData)) {
      errors.push(`缺少字段: ${field}`);
    }
  }
  
  // 检查数据值
  if (kvData.t !== expectedCard.packageType) {
    errors.push(`套餐类型不匹配: 期望 ${expectedCard.packageType}, 实际 ${kvData.t}`);
  }
  
  if (kvData.s !== 'unused') {
    errors.push(`状态不正确: 期望 unused, 实际 ${kvData.s}`);
  }
  
  if (kvData.u !== null) {
    errors.push(`使用者应为null: 实际 ${kvData.u}`);
  }
  
  if (kvData.a !== null) {
    errors.push(`激活时间应为null: 实际 ${kvData.a}`);
  }
  
  if (errors.length > 0) {
    log('red', '❌ 数据格式验证失败:');
    errors.forEach(error => log('red', `   - ${error}`));
    return false;
  }
  
  log('green', '✅ 数据格式验证通过');
  return true;
}

// 主测试函数
async function runTest() {
  try {
    log('blue', '🚀 开始卡密KV同步测试...\n');
    
    // 1. 生成测试卡密
    const card = await generateTestCard();
    
    // 2. 等待一下确保同步完成
    log('yellow', '⏳ 等待KV同步完成...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 3. 检查KV中的数据
    const kvData = await checkCardInKV(card.code);
    
    if (!kvData) {
      log('red', '❌ 测试失败: 卡密未同步到KV');
      return false;
    }
    
    // 4. 验证数据格式
    const isValid = validateKVData(kvData, card);
    
    if (isValid) {
      log('green', '\n🎉 测试成功! 卡密KV同步功能正常工作');
      return true;
    } else {
      log('red', '\n❌ 测试失败: 数据格式不正确');
      return false;
    }
    
  } catch (error) {
    log('red', `\n❌ 测试失败: ${error.message}`);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  runTest()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log('red', `测试异常: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { runTest };
