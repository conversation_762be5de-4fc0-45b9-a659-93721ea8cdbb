/**
 * B后端API接口测试脚本
 * 验证所有B后端API接口功能正常，确保不影响现有功能
 */

require('dotenv').config();
const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:3001',
  apiSecretToken: process.env.API_SECRET_TOKEN,
  timeout: 10000
};

// 测试用户数据
const TEST_DATA = {
  validToken: null, // 将在测试中生成
  validUsername: 'testuser123',
  invalidToken: 'invalid.jwt.token',
  invalidUsername: 'nonexistentuser'
};

// 创建axios实例
const apiClient = axios.create({
  baseURL: TEST_CONFIG.baseURL,
  timeout: TEST_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${TEST_CONFIG.apiSecretToken}`
  }
});

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 测试工具函数
 */
function logTest(testName, status, message = '') {
  testResults.total++;
  if (status === 'PASS') {
    testResults.passed++;
    console.log(`✅ ${testName}: PASS ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: FAIL ${message}`);
    testResults.errors.push(`${testName}: ${message}`);
  }
}

function logInfo(message) {
  console.log(`ℹ️  ${message}`);
}

function logSection(title) {
  console.log(`\n🔍 ${title}`);
  console.log('='.repeat(50));
}

/**
 * 生成测试用的JWT Token
 */
async function generateTestToken() {
  try {
    // 使用现有用户555登录生成token
    const response = await axios.post(`${TEST_CONFIG.baseURL}/api/auth/login`, {
      username: '555',
      password: '888888'
    });

    if (response.data.token) {
      TEST_DATA.validToken = response.data.token;
      TEST_DATA.validUsername = '555';
      logInfo(`生成测试Token成功: ${TEST_DATA.validToken.substring(0, 20)}...`);
      return true;
    }
  } catch (error) {
    logInfo(`登录失败: ${error.response?.data?.error || error.message}`);
    // 尝试使用testuser
    try {
      const response2 = await axios.post(`${TEST_CONFIG.baseURL}/api/auth/login`, {
        username: 'testuser',
        password: 'password'
      });

      if (response2.data.token) {
        TEST_DATA.validToken = response2.data.token;
        TEST_DATA.validUsername = 'testuser';
        logInfo(`使用testuser生成Token成功: ${TEST_DATA.validToken.substring(0, 20)}...`);
        return true;
      }
    } catch (error2) {
      logInfo('无法生成测试Token，将使用模拟Token进行测试');
      // 创建一个模拟的JWT token用于测试
      TEST_DATA.validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.test_signature';
      return false;
    }
  }
}

/**
 * 测试B后端API健康检查
 */
async function testHealthCheck() {
  logSection('健康检查测试');
  
  try {
    // 测试主健康检查
    const response = await apiClient.get('/api/b-backend/health');
    
    if (response.status === 200 && response.data.service === 'b-backend-api') {
      logTest('B后端API健康检查', 'PASS', `状态: ${response.data.status}`);
    } else {
      logTest('B后端API健康检查', 'FAIL', '响应格式不正确');
    }
    
    // 测试认证服务健康检查
    const authHealthResponse = await apiClient.get('/api/b-backend/auth/health');
    if (authHealthResponse.status === 200) {
      logTest('认证服务健康检查', 'PASS');
    } else {
      logTest('认证服务健康检查', 'FAIL');
    }
    
    // 测试用户服务健康检查
    const usersHealthResponse = await apiClient.get('/api/b-backend/users/health');
    if (usersHealthResponse.status === 200) {
      logTest('用户服务健康检查', 'PASS');
    } else {
      logTest('用户服务健康检查', 'FAIL');
    }
    
  } catch (error) {
    logTest('健康检查', 'FAIL', error.message);
  }
}

/**
 * 测试API认证
 */
async function testAuthentication() {
  logSection('API认证测试');
  
  // 测试无认证头
  try {
    await axios.get(`${TEST_CONFIG.baseURL}/api/b-backend/health`);
    logTest('无认证头访问', 'FAIL', '应该返回401错误');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      logTest('无认证头访问', 'PASS', '正确返回401错误');
    } else {
      logTest('无认证头访问', 'FAIL', `意外错误: ${error.message}`);
    }
  }
  
  // 测试错误的认证token
  try {
    await axios.get(`${TEST_CONFIG.baseURL}/api/b-backend/health`, {
      headers: { 'Authorization': 'Bearer wrong_token' }
    });
    logTest('错误认证token', 'FAIL', '应该返回401错误');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      logTest('错误认证token', 'PASS', '正确返回401错误');
    } else {
      logTest('错误认证token', 'FAIL', `意外错误: ${error.message}`);
    }
  }
  
  // 测试正确的认证token
  try {
    const response = await apiClient.get('/api/b-backend/health');
    if (response.status === 200) {
      logTest('正确认证token', 'PASS');
    } else {
      logTest('正确认证token', 'FAIL', `状态码: ${response.status}`);
    }
  } catch (error) {
    logTest('正确认证token', 'FAIL', error.message);
  }
}

/**
 * 测试Token验证接口
 */
async function testTokenVerification() {
  logSection('Token验证接口测试');
  
  // 测试有效token验证
  try {
    const response = await apiClient.post('/api/b-backend/auth/verify', {
      token: TEST_DATA.validToken
    });
    
    if (response.status === 200 && response.data.success && response.data.username) {
      logTest('有效Token验证', 'PASS', `用户名: ${response.data.username}`);
    } else {
      logTest('有效Token验证', 'FAIL', '响应格式不正确');
    }
  } catch (error) {
    logTest('有效Token验证', 'FAIL', error.response?.data?.error || error.message);
  }
  
  // 测试无效token验证
  try {
    const response = await apiClient.post('/api/b-backend/auth/verify', {
      token: TEST_DATA.invalidToken
    });
    logTest('无效Token验证', 'FAIL', '应该返回401错误');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      logTest('无效Token验证', 'PASS', '正确返回401错误');
    } else {
      logTest('无效Token验证', 'FAIL', `意外错误: ${error.message}`);
    }
  }
  
  // 测试缺少token参数
  try {
    const response = await apiClient.post('/api/b-backend/auth/verify', {});
    logTest('缺少Token参数', 'FAIL', '应该返回400错误');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      logTest('缺少Token参数', 'PASS', '正确返回400错误');
    } else {
      logTest('缺少Token参数', 'FAIL', `意外错误: ${error.message}`);
    }
  }
}

/**
 * 测试配额检查接口
 */
async function testQuotaCheck() {
  logSection('配额检查接口测试');
  
  // 测试有效用户配额检查
  try {
    const response = await apiClient.post('/api/b-backend/users/check-quota', {
      username: TEST_DATA.validUsername,
      requiredTier: 'STANDARD',
      requestedChars: 100
    });
    
    if (response.status === 200 && response.data.success) {
      logTest('有效用户配额检查', 'PASS');
    } else {
      logTest('有效用户配额检查', 'FAIL', '响应格式不正确');
    }
  } catch (error) {
    // 配额不足或用户不存在都是正常的业务逻辑
    if (error.response && (error.response.status === 403 || error.response.status === 400)) {
      logTest('有效用户配额检查', 'PASS', `业务逻辑正确: ${error.response.data.error}`);
    } else {
      logTest('有效用户配额检查', 'FAIL', error.message);
    }
  }
  
  // 测试缺少用户名参数
  try {
    const response = await apiClient.post('/api/b-backend/users/check-quota', {
      requiredTier: 'STANDARD',
      requestedChars: 100
    });
    logTest('缺少用户名参数', 'FAIL', '应该返回400错误');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      logTest('缺少用户名参数', 'PASS', '正确返回400错误');
    } else {
      logTest('缺少用户名参数', 'FAIL', `意外错误: ${error.message}`);
    }
  }
}

/**
 * 测试使用量更新接口
 */
async function testUsageUpdate() {
  logSection('使用量更新接口测试');
  
  // 测试有效使用量更新
  try {
    const response = await apiClient.post('/api/b-backend/users/update-usage', {
      username: TEST_DATA.validUsername,
      charCount: 100
    });
    
    if (response.status === 200 && response.data.success) {
      logTest('有效使用量更新', 'PASS');
    } else {
      logTest('有效使用量更新', 'FAIL', '响应格式不正确');
    }
  } catch (error) {
    logTest('有效使用量更新', 'FAIL', error.response?.data?.error || error.message);
  }
  
  // 测试缺少参数
  try {
    const response = await apiClient.post('/api/b-backend/users/update-usage', {
      username: TEST_DATA.validUsername
    });
    logTest('缺少字符数参数', 'FAIL', '应该返回400错误');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      logTest('缺少字符数参数', 'PASS', '正确返回400错误');
    } else {
      logTest('缺少字符数参数', 'FAIL', `意外错误: ${error.message}`);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始B后端API接口测试');
  console.log('='.repeat(50));
  
  logInfo(`测试配置:`);
  logInfo(`- 基础URL: ${TEST_CONFIG.baseURL}`);
  logInfo(`- API密钥: ${TEST_CONFIG.apiSecretToken ? '已配置' : '未配置'}`);
  logInfo(`- 超时时间: ${TEST_CONFIG.timeout}ms`);
  
  // 检查API密钥配置
  if (!TEST_CONFIG.apiSecretToken) {
    console.log('❌ API_SECRET_TOKEN未配置，无法进行测试');
    return;
  }
  
  // 生成测试token
  await generateTestToken();
  
  // 执行测试
  await testHealthCheck();
  await testAuthentication();
  await testTokenVerification();
  await testQuotaCheck();
  await testUsageUpdate();
  
  // 输出测试结果
  console.log('\n📊 测试结果统计');
  console.log('='.repeat(50));
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed}`);
  console.log(`失败: ${testResults.failed}`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  console.log('\n✅ B后端API测试完成');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
