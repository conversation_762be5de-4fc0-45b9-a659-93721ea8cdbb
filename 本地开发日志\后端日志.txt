Database pool initialized: max=20, min=2, env=development
[WEBSOCKET-MANAGER] Cleanup timer started
[INFO] [2025-07-29T01:50:06.176Z] [user:system] [task:N/A] - TTS Application started successfully {"port":"3001","environment":"development","websocketEndpoints":{"single":"ws://localhost:3001/api/tts/ws/generate","dialogue":"ws://localhost:3001/api/tts/ws/dialogue/generate"},"healthCheck":"http://localhost:3001/health"}
Redis connected
Redis ready
CORS: Development mode - allowing all origins (current: http://localhost:4000)
CORS: Development mode - allowing all origins (current: http://localhost:4000)
Get quota error: Error: Token expired
    at verifyToken (D:\myaitts\backend\src\services\authService.js:39:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async D:\myaitts\backend\src\api\user.js:17:22
[INFO] [2025-07-29T02:04:24.069Z] [user:system] [task:N/A] - GET /quota {"status":401,"duration":"5ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
CORS: Development mode - allowing all origins (current: http://localhost:4000)
CORS: Development mode - allowing all origins (current: http://localhost:4000)
[INFO] [2025-07-29T02:04:24.457Z] [user:system] [task:N/A] - POST /refresh {"status":200,"duration":"1ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
CORS: Development mode - allowing all origins (current: http://localhost:4000)
CORS: Development mode - allowing all origins (current: http://localhost:4000)
New database client connected
Executed query {
  text: 'SELECT vip_info, usage_stats FROM users WHERE username = $1',
  duration: 54,
  rows: 1
}
[INFO] [2025-07-29T02:04:24.534Z] [user:system] [task:N/A] - GET /quota {"status":200,"duration":"55ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
CORS: Development mode - allowing all origins (current: http://localhost:4000)
[INFO] [2025-07-29T02:04:59.191Z] [user:system] [task:N/A] - Dialogue TTS WebSocket connection established {"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
Starting dialogue TTS task 7049a56e-cf45-4854-98be-25d4b66d4022 for user 0727
[WEBSOCKET-MANAGER] Starting TTS processing for taskId: 7049a56e-cf45-4854-98be-25d4b66d4022
[WEBSOCKET-MANAGER] Token available: YES
[WEBSOCKET-MANAGER] Token length: 178
[WEBSOCKET-MANAGER] Username: 0727
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 初始化多人对话任务...
Executed query {
  text: 'SELECT vip_info, usage_stats FROM users WHERE username = $1',
  duration: 1,
  rows: 1
}
[QUOTA-CHECK] User 0727 is under new quota rule. Checking quota...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 分析对话结构并启动完全并发处理...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 检测到 6 个句子，将使用最多 6 个并发处理
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 启动完全并发处理模式...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 等待所有句子完成...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 1: tapn1QwocNXk3viVSowa - "小朋友们，大家好！欢迎收听我们的英文绘本..."
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 2: WrPknjKhmIXkCONEtG3j - "Hi everyone! I'm Nic..."
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 3: tapn1QwocNXk3viVSowa - "Nick，我问你一个问题哦。你有没有和你..."
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 4: WrPknjKhmIXkCONEtG3j - "Of course!..."
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 5: tapn1QwocNXk3viVSowa - "那你们是怎么解决的呢？是吵了一架，还是商..."
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 处理句子 6: tapn1QwocNXk3viVSowa - "OK, let's start. 我来读..."
[VOICE-CACHE] Cache miss for voice: tapn1QwocNXk3viVSowa, fetching from database...
[VOICE-CACHE] Cache miss for voice: WrPknjKhmIXkCONEtG3j, fetching from database...
[VOICE-CACHE] Cache miss for voice: tapn1QwocNXk3viVSowa, fetching from database...
[VOICE-CACHE] Cache miss for voice: WrPknjKhmIXkCONEtG3j, fetching from database...
[VOICE-CACHE] Cache miss for voice: tapn1QwocNXk3viVSowa, fetching from database...
[VOICE-CACHE] Cache miss for voice: tapn1QwocNXk3viVSowa, fetching from database...
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 5,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: tapn1QwocNXk3viVSowa -> tapn1QwocNXk3viVSowa
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 36
[INFO] [2025-07-29T02:05:00.474Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.480Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":36,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.481Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":154}
[INFO] [2025-07-29T02:05:00.483Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Initializing NetworkManager...
[DEBUG] [2025-07-29T02:05:00.485Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Creating network client for mode: gateway
[INFO] [2025-07-29T02:05:00.501Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing ProxyGateway...
[DEBUG] [2025-07-29T02:05:00.502Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing WorkerPoolController...
[INFO] [2025-07-29T02:05:00.503Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker pool initialized {"poolSize":10,"portRange":"1081-1090","selectorPrefix":"worker-selector"}
[INFO] [2025-07-29T02:05:00.504Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Initializing WorkerPoolController with Clash API...     
[DEBUG] [2025-07-29T02:05:00.505Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loading nodes from sing-box Clash API...
New database client connected
New database client connected
New database client connected
[WARN] [2025-07-29T02:05:00.547Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: tw-tw-idx-97
[WARN] [2025-07-29T02:05:00.548Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: sg-sg-idx-114
[WARN] [2025-07-29T02:05:00.549Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Skipping invalid node name: jp-jp-idx-118
[INFO] [2025-07-29T02:05:00.549Z] [user:system] [task:N/A] - [TTS-NODE] Loaded nodes for worker pool {"action":"Loaded nodes for worker pool","totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"],"validNodeDetails":[{"name":"sg-sg-013x-idx-0","type":"Shadowsocks"},{"name":"sg-sg-023x-idx-1","type":"Shadowsocks"},{"name":"sg-sg-033x-idx-2","type":"Shadowsocks"},{"name":"sg-sg-043x-idx-3","type":"Trojan"},{"name":"sg-sg-053x-idx-4","type":"Trojan"},{"name":"sg-sg-063x-idx-5","type":"Trojan"},{"name":"sg-sg-073x-idx-6","type":"Trojan"},{"name":"sg-sg-083x-idx-7","type":"Trojan"},{"name":"sg-sg-093x-idx-8","type":"Trojan"},{"name":"jp-jp-013x-idx-9","type":"Shadowsocks"},{"name":"...106 more nodes","type":"truncated"}]}        
[INFO] [2025-07-29T02:05:00.550Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded nodes from sing-box Clash API {"totalNodes":116,"validNodes":116,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...106 more"]}
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 106,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: WrPknjKhmIXkCONEtG3j -> WrPknjKhmIXkCONEtG3j
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 10
[INFO] [2025-07-29T02:05:00.554Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.555Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":10,"voiceId":"WrPknjKhmIXkCONEtG3j","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.555Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","payloadSize":128}
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 110,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: WrPknjKhmIXkCONEtG3j -> WrPknjKhmIXkCONEtG3j
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 22
[INFO] [2025-07-29T02:05:00.558Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.558Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":22,"voiceId":"WrPknjKhmIXkCONEtG3j","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.559Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","payloadSize":140}
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 113,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: tapn1QwocNXk3viVSowa -> tapn1QwocNXk3viVSowa
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 57
[INFO] [2025-07-29T02:05:00.561Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.561Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":57,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.561Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":175}
[INFO] [2025-07-29T02:05:00.563Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded quarantine data for 15 nodes
[DEBUG] [2025-07-29T02:05:00.564Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Validating worker selectors...
[INFO] [2025-07-29T02:05:00.579Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] All worker selectors validated successfully
[INFO] [2025-07-29T02:05:00.579Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] WorkerPoolController initialized successfully {"totalNodes":116,"healthyNodes":116,"quarantinedNodes":0,"totalWorkers":10,"apiType":"clash"}
[DEBUG] [2025-07-29T02:05:00.580Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] WorkerPoolController created and initialized (singleton)
[DEBUG] [2025-07-29T02:05:00.580Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing NetworkAdapter...
[DEBUG] [2025-07-29T02:05:00.580Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] NetworkAdapter initialized
[INFO] [2025-07-29T02:05:00.581Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting health check with 30000ms interval
[INFO] [2025-07-29T02:05:00.581Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting quarantine check with 600000ms interval
[INFO] [2025-07-29T02:05:00.581Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] ProxyGateway initialized successfully {"mode":"gateway","singboxEnabled":true,"healthCheckEnabled":true}
[DEBUG] [2025-07-29T02:05:00.582Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Network client created for mode: gateway
[INFO] [2025-07-29T02:05:00.582Z] [user:system] [task:N/A] - [NETWORK-MANAGER] NetworkManager initialized successfully {"mode":"gateway","gatewayEnabled":true}
[DEBUG] [2025-07-29T02:05:00.582Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.583Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.584Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-013x-idx-0 (attempt 1/101)
[DEBUG] [2025-07-29T02:05:00.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.586Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.587Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-023x-idx-1 (attempt 1/101)
[DEBUG] [2025-07-29T02:05:00.588Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.588Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.589Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-033x-idx-2 (attempt 1/101)
New database client connected
[DEBUG] [2025-07-29T02:05:00.590Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.591Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.591Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-063x-idx-5 (attempt 1/101)
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 146,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: tapn1QwocNXk3viVSowa -> tapn1QwocNXk3viVSowa
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 149
[INFO] [2025-07-29T02:05:00.593Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.594Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":149,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.594Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":267}
[DEBUG] [2025-07-29T02:05:00.594Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.595Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.595Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-093x-idx-8 (attempt 1/101)
New database client connected
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 228,
  rows: 0
}
[VOICE-CACHE] Cached voice mapping: tapn1QwocNXk3viVSowa -> tapn1QwocNXk3viVSowa
[SMART-WORKER-POOL] Processing 1 chunks with 1 intelligent concurrency
Processing chunk 1/1, length: 191
[INFO] [2025-07-29T02:05:00.675Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-29T02:05:00.676Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":191,"voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3"}
[INFO] [2025-07-29T02:05:00.676Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","payloadSize":309}
[DEBUG] [2025-07-29T02:05:00.676Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.677Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:00.677Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-033x-idx-11 (attempt 1/101)
[DEBUG] [2025-07-29T02:05:00.727Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-033x-idx-2 {"nodeTag":"sg-sg-033x-idx-2","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.727Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-033x-idx-2 passed health check 
[DEBUG] [2025-07-29T02:05:00.728Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: sg-sg-033x-idx-2
[INFO] [2025-07-29T02:05:00.728Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"sg-sg-033x-idx-2","originalNodeTag":"sg-sg-033x-idx-2","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"sg-sg-033x-idx-2"}}
[DEBUG] [2025-07-29T02:05:00.731Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5     
[INFO] [2025-07-29T02:05:00.731Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"sg-sg-033x-idx-2","fixedNodeTag":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-29T02:05:00.732Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: sg-sg-033x-idx-2
[DEBUG] [2025-07-29T02:05:00.732Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-29T02:05:00.732Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"sg-sg-033x-idx-2","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.907Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"sg-sg-033x-idx-2","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-29T02:05:00.911Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-093x-idx-8 {"nodeTag":"sg-sg-093x-idx-8","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.912Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-093x-idx-8 passed health check 
[DEBUG] [2025-07-29T02:05:00.912Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-9 to switch to node: sg-sg-093x-idx-8
[INFO] [2025-07-29T02:05:00.913Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-9","targetNode":"sg-sg-093x-idx-8","originalNodeTag":"sg-sg-093x-idx-8","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-9","requestBody":{"name":"sg-sg-093x-idx-8"}}
[DEBUG] [2025-07-29T02:05:00.914Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-013x-idx-0 {"nodeTag":"sg-sg-013x-idx-0","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.914Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-013x-idx-0 passed health check 
[DEBUG] [2025-07-29T02:05:00.914Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-1 to switch to node: sg-sg-013x-idx-0
[INFO] [2025-07-29T02:05:00.915Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"sg-sg-013x-idx-0","originalNodeTag":"sg-sg-013x-idx-0","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"sg-sg-013x-idx-0"}}
[DEBUG] [2025-07-29T02:05:00.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-063x-idx-5 {"nodeTag":"sg-sg-063x-idx-5","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-063x-idx-5 passed health check 
[DEBUG] [2025-07-29T02:05:00.917Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-7 to switch to node: sg-sg-063x-idx-5
[INFO] [2025-07-29T02:05:00.917Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-7","targetNode":"sg-sg-063x-idx-5","originalNodeTag":"sg-sg-063x-idx-5","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-7","requestBody":{"name":"sg-sg-063x-idx-5"}}
[DEBUG] [2025-07-29T02:05:00.920Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-023x-idx-1 {"nodeTag":"sg-sg-023x-idx-1","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.921Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-023x-idx-1 passed health check 
[DEBUG] [2025-07-29T02:05:00.921Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-3 to switch to node: sg-sg-023x-idx-1
[INFO] [2025-07-29T02:05:00.922Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"sg-sg-023x-idx-1","originalNodeTag":"sg-sg-023x-idx-1","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"sg-sg-023x-idx-1"}}
[DEBUG] [2025-07-29T02:05:00.923Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-033x-idx-11 {"nodeTag":"jp-jp-033x-idx-11","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:00.924Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-033x-idx-11 passed health check
[DEBUG] [2025-07-29T02:05:00.924Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-2 to switch to node: jp-jp-033x-idx-11
[INFO] [2025-07-29T02:05:00.925Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-2","targetNode":"jp-jp-033x-idx-11","originalNodeTag":"jp-jp-033x-idx-11","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-2","requestBody":{"name":"jp-jp-033x-idx-11"}}
[DEBUG] [2025-07-29T02:05:00.926Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-9
[INFO] [2025-07-29T02:05:00.927Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-9","newNode":"sg-sg-093x-idx-8","fixedNodeTag":"sg-sg-093x-idx-8"}
[DEBUG] [2025-07-29T02:05:00.927Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-9 successfully switched to node: sg-sg-093x-idx-8
[DEBUG] [2025-07-29T02:05:00.927Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":9,"port":1089,"selector":"worker-selector-9","assignedNode":"sg-sg-093x-idx-8"}
[INFO] [2025-07-29T02:05:00.928Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":9,"workerPort":1089,"assignedNode":"sg-sg-093x-idx-8","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.928Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1089","workerId":9,"assignedNode":"sg-sg-093x-idx-8","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-29T02:05:00.929Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-1     
[INFO] [2025-07-29T02:05:00.930Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"sg-sg-013x-idx-0","fixedNodeTag":"sg-sg-013x-idx-0"}
[DEBUG] [2025-07-29T02:05:00.930Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-1 successfully switched to node: sg-sg-013x-idx-0
[DEBUG] [2025-07-29T02:05:00.930Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":1,"port":1081,"selector":"worker-selector-1","assignedNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-29T02:05:00.930Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"sg-sg-013x-idx-0","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.931Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"sg-sg-013x-idx-0","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-29T02:05:00.937Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-7     
[INFO] [2025-07-29T02:05:00.938Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-7","newNode":"sg-sg-063x-idx-5","fixedNodeTag":"sg-sg-063x-idx-5"}
[DEBUG] [2025-07-29T02:05:00.938Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-7 successfully switched to node: sg-sg-063x-idx-5
[DEBUG] [2025-07-29T02:05:00.939Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":7,"port":1087,"selector":"worker-selector-7","assignedNode":"sg-sg-063x-idx-5"}
[INFO] [2025-07-29T02:05:00.939Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":7,"workerPort":1087,"assignedNode":"sg-sg-063x-idx-5","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.940Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1087","workerId":7,"assignedNode":"sg-sg-063x-idx-5","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-29T02:05:00.942Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-3
[INFO] [2025-07-29T02:05:00.942Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"sg-sg-023x-idx-1","fixedNodeTag":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-29T02:05:00.943Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-3 successfully switched to node: sg-sg-023x-idx-1
[DEBUG] [2025-07-29T02:05:00.943Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":3,"port":1083,"selector":"worker-selector-3","assignedNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-29T02:05:00.943Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"sg-sg-023x-idx-1","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.944Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"sg-sg-023x-idx-1","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[DEBUG] [2025-07-29T02:05:00.945Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-2     
[INFO] [2025-07-29T02:05:00.945Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-2","newNode":"jp-jp-033x-idx-11","fixedNodeTag":"jp-jp-033x-idx-11"}
[DEBUG] [2025-07-29T02:05:00.946Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-2 successfully switched to node: jp-jp-033x-idx-11
[DEBUG] [2025-07-29T02:05:00.946Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":2,"port":1082,"selector":"worker-selector-2","assignedNode":"jp-jp-033x-idx-11"}
[INFO] [2025-07-29T02:05:00.946Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":2,"workerPort":1082,"assignedNode":"jp-jp-033x-idx-11","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:00.946Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1082","workerId":2,"assignedNode":"jp-jp-033x-idx-11","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-29T02:05:01.017Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":9,"assignedNode":"sg-sg-093x-idx-8","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":9,\"assignedNode\":\"sg-sg-093x-idx-8\",\"attempt\":1,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":9,"assignedNode":"sg-sg-093x-idx-8","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":9,\"assignedNode\":\"sg-sg-093x-idx-8\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myai"}
[WARN] [2025-07-29T02:05:01.019Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":9,"nodeTag":"sg-sg-093x-idx-8"}
[WARN] [2025-07-29T02:05:01.020Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-093x-idx-8 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-29T02:05:01.020Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-093x-idx-8","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":100,"totalQuarantinedNodes":16}
[INFO] [2025-07-29T02:05:01.021Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"sg-sg-093x-idx-8","willRetry":true}
[DEBUG] [2025-07-29T02:05:01.022Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":9,"port":1089,"currentNode":"sg-sg-093x-idx-8"}
[INFO] [2025-07-29T02:05:01.022Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":9,"assignedNode":"sg-sg-093x-idx-8","attempt":1}
[DEBUG] [2025-07-29T02:05:01.022Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node jp-jp-063x-idx-14 (attempt 1/100)
[ERROR] [2025-07-29T02:05:01.028Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","workerId":7,"assignedNode":"sg-sg-063x-idx-5","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1\",\"workerId\":7,\"assignedNode\":\"sg-sg-063x-idx-5\",\"attempt\":1,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","workerId":7,"assignedNode":"sg-sg-063x-idx-5","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1\",\"workerId\":7,\"assignedNode\":\"sg-sg-063x-idx-5\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myai"}
[WARN] [2025-07-29T02:05:01.029Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","workerId":7,"nodeTag":"sg-sg-063x-idx-5"}
[WARN] [2025-07-29T02:05:01.029Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-063x-idx-5 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-29T02:05:01.030Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-063x-idx-5","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":99,"totalQuarantinedNodes":17}
[INFO] [2025-07-29T02:05:01.030Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"sg-sg-063x-idx-5","willRetry":true}
[DEBUG] [2025-07-29T02:05:01.030Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":7,"port":1087,"currentNode":"sg-sg-063x-idx-5"}
[INFO] [2025-07-29T02:05:01.030Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":7,"assignedNode":"sg-sg-063x-idx-5","attempt":1}
[DEBUG] [2025-07-29T02:05:01.031Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-016x-idx-18 (attempt 1/99)
[DEBUG] [2025-07-29T02:05:01.032Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 17 nodes
[DEBUG] [2025-07-29T02:05:01.033Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 17 nodes
[DEBUG] [2025-07-29T02:05:01.113Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-063x-idx-14 {"nodeTag":"jp-jp-063x-idx-14","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:01.114Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node jp-jp-063x-idx-14 passed health check
[DEBUG] [2025-07-29T02:05:01.115Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: jp-jp-063x-idx-14
[INFO] [2025-07-29T02:05:01.115Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"jp-jp-063x-idx-14","originalNodeTag":"jp-jp-063x-idx-14","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"jp-jp-063x-idx-14"}}
[DEBUG] [2025-07-29T02:05:01.120Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6     
[INFO] [2025-07-29T02:05:01.121Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"jp-jp-063x-idx-14","fixedNodeTag":"jp-jp-063x-idx-14"}
[DEBUG] [2025-07-29T02:05:01.121Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: jp-jp-063x-idx-14
[DEBUG] [2025-07-29T02:05:01.122Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"jp-jp-063x-idx-14"}
[INFO] [2025-07-29T02:05:01.122Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"jp-jp-063x-idx-14","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","attempt":2,"maxAttempts":2}
[INFO] [2025-07-29T02:05:01.123Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"jp-jp-063x-idx-14","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[DEBUG] [2025-07-29T02:05:01.144Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-016x-idx-18 {"nodeTag":"kr-kr-016x-idx-18","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:01.145Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-016x-idx-18 passed health check
[DEBUG] [2025-07-29T02:05:01.145Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-8 to switch to node: kr-kr-016x-idx-18
[INFO] [2025-07-29T02:05:01.146Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-8","targetNode":"kr-kr-016x-idx-18","originalNodeTag":"kr-kr-016x-idx-18","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-8","requestBody":{"name":"kr-kr-016x-idx-18"}}
[DEBUG] [2025-07-29T02:05:01.147Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-8     
[INFO] [2025-07-29T02:05:01.148Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-8","newNode":"kr-kr-016x-idx-18","fixedNodeTag":"kr-kr-016x-idx-18"}
[DEBUG] [2025-07-29T02:05:01.148Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-8 successfully switched to node: kr-kr-016x-idx-18
[DEBUG] [2025-07-29T02:05:01.148Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":8,"port":1088,"selector":"worker-selector-8","assignedNode":"kr-kr-016x-idx-18"}
[INFO] [2025-07-29T02:05:01.149Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":8,"workerPort":1088,"assignedNode":"kr-kr-016x-idx-18","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","attempt":2,"maxAttempts":2}
[INFO] [2025-07-29T02:05:01.149Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1088","workerId":8,"assignedNode":"kr-kr-016x-idx-18","method":"POST","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-29T02:05:01.173Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":6,"assignedNode":"jp-jp-063x-idx-14","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"jp-jp-063x-idx-14\",\"attempt\":2,\"maxAttempts\":2}","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":6,"assignedNode":"jp-jp-063x-idx-14","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"POST\",\"url\":\"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1\",\"workerId\":6,\"assignedNode\":\"jp-jp-063x-idx-14\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\mya"}
[WARN] [2025-07-29T02:05:01.174Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","workerId":6,"nodeTag":"jp-jp-063x-idx-14"}
[WARN] [2025-07-29T02:05:01.174Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-063x-idx-14 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-29T02:05:01.175Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-063x-idx-14","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":98,"totalQuarantinedNodes":18}
[DEBUG] [2025-07-29T02:05:01.176Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"jp-jp-063x-idx-14"}
[INFO] [2025-07-29T02:05:01.176Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"jp-jp-063x-idx-14","attempt":2}
[ERROR] [2025-07-29T02:05:01.177Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-29T02:05:01.178Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[ERROR] [2025-07-29T02:05:01.178Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in gateway mode {"mode":"gateway","error":"fetch failed","totalDuration":"584ms"} {"mode":"gateway","error":"[TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"584ms\"}","totalDuration":"584ms","stack":"Error: [TTS-ERROR] Failed in gateway mode {\"mode\":\"gateway\",\"error\":\"fetch failed\",\"totalDuration\":\"584ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechWithGateway (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1527:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsUti"}
[ERROR] [2025-07-29T02:05:01.179Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in smart mode {"mode":"smart","error":"fetch failed","duration":"586ms"} {"mode":"smart","error":"[TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"586ms\"}","duration":"586ms","stack":"Error: [TTS-ERROR] Failed in smart mode {\"mode\":\"smart\",\"error\":\"fetch failed\",\"duration\":\"586ms\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at generateSpeechSmart (D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:1667:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\myaitts\\backend\\src\\utils\\ttsUtils.js:430:29"}
Chunk 1 failed: fetch failed
1 chunks failed, attempting retry...
[DEBUG] [2025-07-29T02:05:01.180Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 18 nodes
[INFO] [2025-07-29T02:05:03.134Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":8,"assignedNode":"kr-kr-016x-idx-18","attempt":2}
[DEBUG] [2025-07-29T02:05:03.134Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":8,"port":1088,"currentNode":"kr-kr-016x-idx-18"}
[INFO] [2025-07-29T02:05:03.135Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":8,"assignedNode":"kr-kr-016x-idx-18","attempt":2}
[DEBUG] [2025-07-29T02:05:03.135Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","status":200,"duration":"2544ms"}
[INFO] [2025-07-29T02:05:03.136Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j","status":200,"duration":"2581ms","responseOk":true}
[INFO] [2025-07-29T02:05:03.175Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":18435,"totalDuration":"2620ms","requestDuration":"2581ms"}
[INFO] [2025-07-29T02:05:03.176Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":18435,"duration":"2622ms"}  
[PERFORMANCE] Processed 1 chunks in 2622ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 4 (WrPknjKhmIXkCONEtG3j) 处理完成
[INFO] [2025-07-29T02:05:03.191Z] [user:system] [task:N/A] - [TTS-PROXY] Using fallback mode {"action":"Using fallback mode","strategy":"direct-first-then-proxy"}
[INFO] [2025-07-29T02:05:03.191Z] [user:system] [task:N/A] - [TTS-DIRECT] Attempting direct connection first {"action":"Attempting direct connection first"}
[INFO] [2025-07-29T02:05:03.192Z] [user:system] [task:N/A] - [TTS-DIRECT] Starting direct ElevenLabs request {"action":"Starting direct ElevenLabs request","voiceId":"tapn1QwocNXk3viVSowa","modelId":"eleven_v3","textLength":149}
[INFO] [2025-07-29T02:05:03.486Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[DEBUG] [2025-07-29T02:05:03.487Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":3,"port":1083,"currentNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-29T02:05:03.487Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"sg-sg-023x-idx-1","attempt":1}
[DEBUG] [2025-07-29T02:05:03.488Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j?allow_unauthenticated=1","status":200,"duration":"2902ms"}
[INFO] [2025-07-29T02:05:03.488Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/WrPknjKhmIXkCONEtG3j","status":200,"duration":"2929ms","responseOk":true}
[INFO] [2025-07-29T02:05:03.526Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":25122,"totalDuration":"2968ms","requestDuration":"2929ms"}
[INFO] [2025-07-29T02:05:03.527Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":25122,"duration":"2970ms"}  
[PERFORMANCE] Processed 1 chunks in 2970ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 2 (WrPknjKhmIXkCONEtG3j) 处理完成
[DEBUG] [2025-07-29T02:05:05.594Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Starting quarantine pool check...
[INFO] [2025-07-29T02:05:05.595Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking 18 quarantined nodes
[DEBUG] [2025-07-29T02:05:05.595Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-053x-idx-4 (temporary)
[DEBUG] [2025-07-29T02:05:05.731Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-053x-idx-4 {"nodeTag":"sg-sg-053x-idx-4","status":204,"healthy":true,"duration":5000}
[INFO] [2025-07-29T02:05:05.732Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-053x-idx-4 restored to healthy pool {"consecutiveSuccesses":2,"quarantineType":"temporary","forceRestore":false}
[INFO] [2025-07-29T02:05:05.733Z] [user:system] [task:N/A] - [TTS-NODE] Node restored to healthy pool {"action":"Node restored to healthy pool","nodeTag":"sg-sg-053x-idx-4","consecutiveSuccesses":2,"quarantineType":"temporary","healthyNodes":99,"forceRestore":false}
[INFO] [2025-07-29T02:05:05.733Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-053x-idx-4 recovered successfully
[DEBUG] [2025-07-29T02:05:05.733Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-083x-idx-7 (temporary)
[DEBUG] [2025-07-29T02:05:05.735Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 17 nodes
[DEBUG] [2025-07-29T02:05:05.894Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-083x-idx-7 {"nodeTag":"sg-sg-083x-idx-7","status":204,"healthy":true,"duration":5000}
[INFO] [2025-07-29T02:05:05.895Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-083x-idx-7 restored to healthy pool {"consecutiveSuccesses":2,"quarantineType":"temporary","forceRestore":false}
[INFO] [2025-07-29T02:05:05.896Z] [user:system] [task:N/A] - [TTS-NODE] Node restored to healthy pool {"action":"Node restored to healthy pool","nodeTag":"sg-sg-083x-idx-7","consecutiveSuccesses":2,"quarantineType":"temporary","healthyNodes":100,"forceRestore":false}
[INFO] [2025-07-29T02:05:05.896Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-083x-idx-7 recovered successfully
[DEBUG] [2025-07-29T02:05:05.896Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-043x-idx-3 (temporary)
[DEBUG] [2025-07-29T02:05:05.897Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 16 nodes
[DEBUG] [2025-07-29T02:05:05.984Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-043x-idx-3 {"nodeTag":"sg-sg-043x-idx-3","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:05.985Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-043x-idx-3 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:05.985Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-043x-idx-3 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:05.986Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-073x-idx-6 (temporary)
[DEBUG] [2025-07-29T02:05:06.099Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-073x-idx-6 {"nodeTag":"sg-sg-073x-idx-6","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.100Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-073x-idx-6 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.101Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-073x-idx-6 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.101Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-023x-idx-10 (temporary)
[DEBUG] [2025-07-29T02:05:06.189Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-023x-idx-10 {"nodeTag":"jp-jp-023x-idx-10","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.190Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-023x-idx-10 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.190Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-023x-idx-10 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.190Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-013x-idx-9 (temporary)
[DEBUG] [2025-07-29T02:05:06.303Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-013x-idx-9 {"nodeTag":"jp-jp-013x-idx-9","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.304Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-013x-idx-9 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.305Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-013x-idx-9 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.305Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-053x-idx-13 (temporary)
[DEBUG] [2025-07-29T02:05:06.393Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-053x-idx-13 {"nodeTag":"jp-jp-053x-idx-13","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.394Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-053x-idx-13 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.394Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-053x-idx-13 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.394Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-073x-idx-15 (temporary)
[DEBUG] [2025-07-29T02:05:06.507Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-073x-idx-15 {"nodeTag":"jp-jp-073x-idx-15","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.508Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-073x-idx-15 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.508Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-073x-idx-15 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.509Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-093x-idx-17 (temporary)
[DEBUG] [2025-07-29T02:05:06.596Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-093x-idx-17 {"nodeTag":"jp-jp-093x-idx-17","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.597Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-093x-idx-17 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.597Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-093x-idx-17 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.598Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: kr-kr-046x-idx-21 (temporary)
[DEBUG] [2025-07-29T02:05:06.710Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-046x-idx-21 {"nodeTag":"kr-kr-046x-idx-21","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.711Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node kr-kr-046x-idx-21 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.712Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node kr-kr-046x-idx-21 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.712Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: kr-kr-066x-idx-23 (temporary)
[DEBUG] [2025-07-29T02:05:06.800Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-066x-idx-23 {"nodeTag":"kr-kr-066x-idx-23","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.800Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node kr-kr-066x-idx-23 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.801Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node kr-kr-066x-idx-23 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.801Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: kr-kr-036x-idx-20 (temporary)
[DEBUG] [2025-07-29T02:05:06.915Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-036x-idx-20 {"nodeTag":"kr-kr-036x-idx-20","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:06.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node kr-kr-036x-idx-20 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:06.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node kr-kr-036x-idx-20 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:06.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: kr-kr-096x-idx-26 (temporary)
[DEBUG] [2025-07-29T02:05:07.004Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-096x-idx-26 {"nodeTag":"kr-kr-096x-idx-26","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.005Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node kr-kr-096x-idx-26 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.005Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node kr-kr-096x-idx-26 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:07.006Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: us-us-043x-idx-30 (temporary)
[DEBUG] [2025-07-29T02:05:07.119Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-043x-idx-30 {"nodeTag":"us-us-043x-idx-30","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.120Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node us-us-043x-idx-30 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.120Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node us-us-043x-idx-30 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:07.120Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: us-us-063x-idx-32 (temporary)
[DEBUG] [2025-07-29T02:05:07.208Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-063x-idx-32 {"nodeTag":"us-us-063x-idx-32","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.209Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node us-us-063x-idx-32 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.209Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node us-us-063x-idx-32 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:07.209Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-093x-idx-8 (temporary)
[DEBUG] [2025-07-29T02:05:07.322Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-093x-idx-8 {"nodeTag":"sg-sg-093x-idx-8","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.323Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-093x-idx-8 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.323Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-093x-idx-8 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:07.324Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: sg-sg-063x-idx-5 (temporary)
[DEBUG] [2025-07-29T02:05:07.411Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-063x-idx-5 {"nodeTag":"sg-sg-063x-idx-5","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.412Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node sg-sg-063x-idx-5 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.412Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node sg-sg-063x-idx-5 health check passed but needs more successes
[DEBUG] [2025-07-29T02:05:07.412Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Checking node: jp-jp-063x-idx-14 (temporary)
[DEBUG] [2025-07-29T02:05:07.525Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node jp-jp-063x-idx-14 {"nodeTag":"jp-jp-063x-idx-14","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:07.526Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Node jp-jp-063x-idx-14 health check passed but needs more successes {"consecutiveSuccesses":1,"requiredSuccesses":2,"quarantineType":"temporary"}
[DEBUG] [2025-07-29T02:05:07.526Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Node jp-jp-063x-idx-14 health check passed but needs more successes
[INFO] [2025-07-29T02:05:07.526Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Check completed: 18 checked, 2 recovered
[INFO] [2025-07-29T02:05:08.512Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[DEBUG] [2025-07-29T02:05:08.512Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":1,"port":1081,"currentNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-29T02:05:08.513Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"sg-sg-013x-idx-0","attempt":1}
[WARN] [2025-07-29T02:05:08.513Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","status":200,"duration":"7930ms"}
[INFO] [2025-07-29T02:05:08.514Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"8033ms","responseOk":true}
[INFO] [2025-07-29T02:05:08.621Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":97847,"totalDuration":"8145ms","requestDuration":"8033ms"}
[INFO] [2025-07-29T02:05:08.622Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":97847,"duration":"8151ms"}  
[PERFORMANCE] Processed 1 chunks in 8156ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 1 (tapn1QwocNXk3viVSowa) 处理完成
[INFO] [2025-07-29T02:05:14.329Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":5,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[DEBUG] [2025-07-29T02:05:14.329Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-29T02:05:14.329Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"sg-sg-033x-idx-2","attempt":1}
[WARN] [2025-07-29T02:05:14.330Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","status":200,"duration":"13742ms"}
[INFO] [2025-07-29T02:05:14.330Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"13769ms","responseOk":true}
[INFO] [2025-07-29T02:05:14.463Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":229922,"totalDuration":"13902ms","requestDuration":"13769ms"}
[INFO] [2025-07-29T02:05:14.463Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":229922,"duration":"13903ms"}
[PERFORMANCE] Processed 1 chunks in 13904ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 3 (tapn1QwocNXk3viVSowa) 处理完成
[INFO] [2025-07-29T02:05:18.294Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"15102ms","responseOk":true}
[INFO] [2025-07-29T02:05:18.966Z] [user:system] [task:N/A] - [TTS-DIRECT] Direct request successful {"action":"Direct request successful","audioSize":236191,"requestDuration":"15102ms","totalDuration":"15774ms"}
[INFO] [2025-07-29T02:05:18.967Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via direct-fallback {"mode":"direct-fallback","audioSize":236191,"duration":"15776ms"}
Retry successful for chunk 1
[PERFORMANCE] Processed 1 chunks in 586ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 6 (tapn1QwocNXk3viVSowa) 处理完成
Database client removed
Database client removed
Database client removed
Database client removed
[DEBUG] [2025-07-29T02:05:30.583Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-29T02:05:30.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-056x-idx-22 (attempt 1/100)
Database client removed
Database client removed
[DEBUG] [2025-07-29T02:05:30.722Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-056x-idx-22 {"nodeTag":"kr-kr-056x-idx-22","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:30.722Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-056x-idx-22 passed health check
[DEBUG] [2025-07-29T02:05:30.723Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: kr-kr-056x-idx-22
[INFO] [2025-07-29T02:05:30.723Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"kr-kr-056x-idx-22","originalNodeTag":"kr-kr-056x-idx-22","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"kr-kr-056x-idx-22"}}
[DEBUG] [2025-07-29T02:05:30.724Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5     
[INFO] [2025-07-29T02:05:30.724Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"kr-kr-056x-idx-22","fixedNodeTag":"kr-kr-056x-idx-22"}
[DEBUG] [2025-07-29T02:05:30.725Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: kr-kr-056x-idx-22
[DEBUG] [2025-07-29T02:05:30.725Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"kr-kr-056x-idx-22"}
[INFO] [2025-07-29T02:05:30.725Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"kr-kr-056x-idx-22","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:05:30.726Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"kr-kr-056x-idx-22","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[ERROR] [2025-07-29T02:05:30.768Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-056x-idx-22","attempt":1,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-056x-idx-22\",\"attempt\":1,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"kr-kr-056x-idx-22","attempt":1,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"kr-kr-056x-idx-22\",\"attempt\":1,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-29T02:05:30.769Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":5,"nodeTag":"kr-kr-056x-idx-22"}
[WARN] [2025-07-29T02:05:30.769Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-056x-idx-22 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-29T02:05:30.770Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-056x-idx-22","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":99,"totalQuarantinedNodes":17}
[INFO] [2025-07-29T02:05:30.770Z] [user:system] [task:N/A] - [TTS-GATEWAY] Gateway request failed, retrying with new node {"action":"Gateway request failed, retrying with new node","attempt":1,"maxAttempts":2,"error":"fetch failed","failedNode":"kr-kr-056x-idx-22","willRetry":true}
[DEBUG] [2025-07-29T02:05:30.771Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"kr-kr-056x-idx-22"}
[INFO] [2025-07-29T02:05:30.771Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"kr-kr-056x-idx-22","attempt":1}
[DEBUG] [2025-07-29T02:05:30.771Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node kr-kr-086x-idx-25 (attempt 1/99)
[DEBUG] [2025-07-29T02:05:30.773Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 17 nodes
[DEBUG] [2025-07-29T02:05:30.865Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node kr-kr-086x-idx-25 {"nodeTag":"kr-kr-086x-idx-25","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:05:30.866Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node kr-kr-086x-idx-25 passed health check
[DEBUG] [2025-07-29T02:05:30.867Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: kr-kr-086x-idx-25
[INFO] [2025-07-29T02:05:30.867Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"kr-kr-086x-idx-25","originalNodeTag":"kr-kr-086x-idx-25","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"kr-kr-086x-idx-25"}}
[DEBUG] [2025-07-29T02:05:30.869Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6     
[INFO] [2025-07-29T02:05:30.869Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"kr-kr-086x-idx-25","fixedNodeTag":"kr-kr-086x-idx-25"}
[DEBUG] [2025-07-29T02:05:30.870Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: kr-kr-086x-idx-25
[DEBUG] [2025-07-29T02:05:30.870Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"kr-kr-086x-idx-25"}
[INFO] [2025-07-29T02:05:30.870Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"kr-kr-086x-idx-25","method":"GET","url":"https://httpbin.org/ip","attempt":2,"maxAttempts":2}
[INFO] [2025-07-29T02:05:30.871Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"kr-kr-086x-idx-25","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":2}
[ERROR] [2025-07-29T02:05:30.910Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":6,"assignedNode":"kr-kr-086x-idx-25","attempt":2,"maxAttempts":2} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":6,\"assignedNode\":\"kr-kr-086x-idx-25\",\"attempt\":2,\"maxAttempts\":2}","method":"GET","url":"https://httpbin.org/ip","workerId":6,"assignedNode":"kr-kr-086x-idx-25","attempt":2,"maxAttempts":2,"stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":6,\"assignedNode\":\"kr-kr-086x-idx-25\",\"attempt\":2,\"maxAttempts\":2}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:207:23)\n    at "}
[WARN] [2025-07-29T02:05:30.911Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":6,"nodeTag":"kr-kr-086x-idx-25"}
[WARN] [2025-07-29T02:05:30.911Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: kr-kr-086x-idx-25 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-29T02:05:30.912Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"kr-kr-086x-idx-25","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":98,"totalQuarantinedNodes":18}
[DEBUG] [2025-07-29T02:05:30.912Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"kr-kr-086x-idx-25"}
[INFO] [2025-07-29T02:05:30.912Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"kr-kr-086x-idx-25","attempt":2}
[ERROR] [2025-07-29T02:05:30.913Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-29T02:05:30.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-29T02:05:30.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[WARN] [2025-07-29T02:05:30.914Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: jp-jp-033x-idx-11 {"reason":"Health check failed"}
[INFO] [2025-07-29T02:05:30.914Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"jp-jp-033x-idx-11","reason":"Health check failed","quarantineType":"temporary","remainingHealthyNodes":97,"totalQuarantinedNodes":19}
[DEBUG] [2025-07-29T02:05:30.915Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753754730583}
[DEBUG] [2025-07-29T02:05:30.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 19 nodes
[DEBUG] [2025-07-29T02:05:30.916Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 19 nodes
[INFO] [2025-07-29T02:05:39.159Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":2,"assignedNode":"jp-jp-033x-idx-11","attempt":1}
[DEBUG] [2025-07-29T02:05:39.242Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":2,"port":1082,"currentNode":"jp-jp-033x-idx-11"}
[INFO] [2025-07-29T02:05:39.340Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":2,"assignedNode":"jp-jp-033x-idx-11","attempt":1}
[WARN] [2025-07-29T02:05:39.388Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa?allow_unauthenticated=1","status":200,"duration":"38711ms"}
[INFO] [2025-07-29T02:05:39.445Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/tapn1QwocNXk3viVSowa","status":200,"duration":"38769ms","responseOk":true}
[INFO] [2025-07-29T02:05:39.787Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":657494,"totalDuration":"39111ms","requestDuration":"38769ms"}
[INFO] [2025-07-29T02:05:39.838Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":657494,"duration":"39163ms"}
[PERFORMANCE] Processed 1 chunks in 39163ms with 1 concurrency (1/1 successful)
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 句子 5 (tapn1QwocNXk3viVSowa) 处理完成
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 完全并发处理成功完成
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 按原始顺序重新组装音频...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 音频重组完成，共 6 个音频片段
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 正在合并所有对话音频...
[INTERNAL-PROGRESS] 7049a56e-cf45-4854-98be-25d4b66d4022: 正在保存音频文件...
Audio file stored: \var\data\tts-app\audios\7049a56e-cf45-4854-98be-25d4b66d4022.mp3, size: 1265011 bytes
New database client connected
Updated usage for user 0727: +465 chars
[TTS-PROCESSOR-DIALOGUE] Generating secure URLs for taskId: 7049a56e-cf45-4854-98be-25d4b66d4022
[TTS-PROCESSOR-DIALOGUE] Token received: YES
[TTS-PROCESSOR-DIALOGUE] Token length: 178
[TTS-PROCESSOR-DIALOGUE] Generated secure streamUrl: http://localhost:3001/api/tts/stream/7049a56e-cf45-4854-98be-25d4b66d4022
[TTS-PROCESSOR-DIALOGUE] Generated secure downloadUrl: http://localhost:3001/api/tts/download/7049a56e-cf45-4854-98be-25d4b66d4022
[TTS-PROCESSOR-DIALOGUE] Token will be passed via Authorization header for security
[WEBSOCKET-MANAGER] Task 7049a56e-cf45-4854-98be-25d4b66d4022 finished with type: complete, scheduling connection close
[WEBSOCKET-MANAGER] Closing connection for task 7049a56e-cf45-4854-98be-25d4b66d4022, code: 1000, reason: Task finished: complete
[WEBSOCKET-MANAGER] Cleaned up connection for task 7049a56e-cf45-4854-98be-25d4b66d4022
WebSocket closed for task 7049a56e-cf45-4854-98be-25d4b66d4022
CORS: Development mode - allowing all origins (current: http://localhost:4000)
CORS: Development mode - allowing all origins (current: http://localhost:4000)
[STREAM] Request for taskId: 7049a56e-cf45-4854-98be-25d4b66d4022, token source: header
[STREAM] Token verified for user: 0727, taskId: 7049a56e-cf45-4854-98be-25d4b66d4022
[STREAM] Checking file path: \var\data\tts-app\audios\7049a56e-cf45-4854-98be-25d4b66d4022.mp3
[STREAM] File found, size: 1265011 bytes
[INFO] [2025-07-29T02:05:43.495Z] [user:system] [task:N/A] - GET /stream/7049a56e-cf45-4854-98be-25d4b66d4022 {"status":200,"duration":"23ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[DEBUG] [2025-07-29T02:06:00.585Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-29T02:06:00.586Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node us-us-033x-idx-29 (attempt 1/97)
[DEBUG] [2025-07-29T02:06:00.719Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node us-us-033x-idx-29 {"nodeTag":"us-us-033x-idx-29","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-29T02:06:00.720Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node us-us-033x-idx-29 passed health check
[DEBUG] [2025-07-29T02:06:00.720Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-6 to switch to node: us-us-033x-idx-29
[INFO] [2025-07-29T02:06:00.720Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-6","targetNode":"us-us-033x-idx-29","originalNodeTag":"us-us-033x-idx-29","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-6","requestBody":{"name":"us-us-033x-idx-29"}}
[DEBUG] [2025-07-29T02:06:00.722Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-6     
[INFO] [2025-07-29T02:06:00.722Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-6","newNode":"us-us-033x-idx-29","fixedNodeTag":"us-us-033x-idx-29"}
[DEBUG] [2025-07-29T02:06:00.723Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-6 successfully switched to node: us-us-033x-idx-29
[DEBUG] [2025-07-29T02:06:00.723Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":6,"port":1086,"selector":"worker-selector-6","assignedNode":"us-us-033x-idx-29"}
[INFO] [2025-07-29T02:06:00.723Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":6,"workerPort":1086,"assignedNode":"us-us-033x-idx-29","method":"GET","url":"https://httpbin.org/ip","attempt":1,"maxAttempts":2}
[INFO] [2025-07-29T02:06:00.724Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1086","workerId":6,"assignedNode":"us-us-033x-idx-29","method":"GET","hasProxy":true,"proxyType":"dispatcher","attempt":1}
[INFO] [2025-07-29T02:06:01.826Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":6,"assignedNode":"us-us-033x-idx-29","attempt":1}
[DEBUG] [2025-07-29T02:06:01.827Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":6,"port":1086,"currentNode":"us-us-033x-idx-29"}
[INFO] [2025-07-29T02:06:01.827Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":6,"assignedNode":"us-us-033x-idx-29","attempt":1}
[DEBUG] [2025-07-29T02:06:01.827Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"1242ms"}
[DEBUG] [2025-07-29T02:06:01.828Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-29T02:06:01.828Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753754760585}
[INFO] [2025-07-29T02:06:18.665Z] [user:system] [task:N/A] - SIGINT received, shutting down gracefully
[WEBSOCKET-MANAGER] Cleanup timer stopped
[INFO] [2025-07-29T02:06:18.668Z] [user:system] [task:N/A] - All connections closed successfully