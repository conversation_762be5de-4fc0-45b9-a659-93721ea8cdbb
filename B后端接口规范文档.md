# B后端接口规范文档

## 📋 概述

本文档定义了B后端（Ubuntu PostgreSQL服务器）需要为A后端（Cloudflare Worker）提供的所有接口功能。基于远程数据库方案分析中的"方案一"，B后端作为数据写入的权威源，负责处理所有用户数据的写操作，并同步到Cloudflare KV。

## 🏗️ 架构原则

- **PostgreSQL为权威数据源**：所有写操作必须先写入PostgreSQL
- **单向数据同步**：PostgreSQL → Cloudflare KV
- **A后端读取优化**：优先从KV读取，写操作调用B后端API
- **数据一致性保证**：B后端负责确保PostgreSQL和KV的最终一致性

## 🔐 认证机制

### API认证
所有接口都需要在请求头中包含认证信息：

```http
Authorization: Bearer <API_SECRET_TOKEN>
Content-Type: application/json
```

### 环境变量配置
```bash
# B后端需要配置的环境变量
CLOUDFLARE_KV_API_TOKEN=<KV写入令牌>
CLOUDFLARE_ACCOUNT_ID=<账户ID>
CLOUDFLARE_NAMESPACE_ID_USERS=<用户数据KV命名空间ID>
CLOUDFLARE_NAMESPACE_ID_CARDS=<卡密数据KV命名空间ID>
API_SECRET_TOKEN=<与A后端共享的认证密钥>
```

## 📊 数据结构定义

### 用户数据结构
```json
{
  "username": "string",
  "passwordHash": "string",
  "email": "string",
  "createdAt": "number",
  "quota": {
    "daily": "number",
    "used": "number", 
    "resetAt": "number"
  },
  "vip": {
    "expireAt": "number",
    "type": "string|null",
    "quotaChars": "number",
    "usedChars": "number"
  },
  "usage": {
    "totalChars": "number",
    "monthlyChars": "number",
    "monthlyResetAt": "number"
  },
  "passwordUpdatedAt": "number"
}
```

### 卡密数据结构
```json
{
  "c": "string",     // 卡密代码
  "t": "string",     // 套餐类型 (M/Q/H/PM/PQ/PH/PT)
  "s": "string",     // 状态 (unused/using/used)
  "u": "string",     // 使用用户
  "a": "number"      // 激活时间
}
```

## 🔧 核心接口定义

### 1. 用户管理接口

#### 1.1 用户注册完成
```http
POST /api/users/register
```

**请求体：**
```json
{
  "username": "string",
  "passwordHash": "string",
  "email": "string",
  "createdAt": "number"
}
```

**响应：**
```json
{
  "success": true,
  "message": "用户注册成功",
  "userData": {用户数据结构}
}
```

**功能要求：**
- 检查用户名和邮箱唯一性
- 写入PostgreSQL用户表
- 同步到KV：`user:${username}` 和 `email:${email}`
- 初始化默认配额和VIP信息

**说明：** A后端完成邮箱验证后调用此接口完成用户注册

#### 1.2 密码重置
```http
POST /api/users/reset-password
```

**请求体：**
```json
{
  "username": "string",
  "newPasswordHash": "string",
  "passwordUpdatedAt": "number"
}
```

**响应：**
```json
{
  "success": true,
  "message": "密码重置成功"
}
```

**功能要求：**
- 更新PostgreSQL中的密码哈希
- 同步更新KV中的用户数据

**说明：** A后端完成邮箱验证后调用此接口重置密码

### 2. VIP和配额管理接口

#### 2.1 使用卡密
```http
POST /api/vip/use-card
```

**请求体：**
```json
{
  "username": "string",
  "cardCode": "string",
  "activatedAt": "number"
}
```

**响应：**
```json
{
  "success": true,
  "vipInfo": {
    "expireAt": "number",
    "type": "string",
    "quotaChars": "number",
    "usedChars": "number"
  }
}
```

**功能要求：**
- 验证卡密有效性和状态
- 计算新的VIP到期时间和配额
- 更新用户VIP信息到PostgreSQL
- 标记卡密为已使用
- 同步更新KV中的用户数据和卡密数据

#### 2.2 更新用户使用量
```http
POST /api/users/update-usage
```

**请求体：**
```json
{
  "username": "string",
  "charCount": "number",
  "timestamp": "number"
}
```

**响应：**
```json
{
  "success": true,
  "updatedUsage": {
    "vip": {
      "usedChars": "number",
      "quotaChars": "number"
    },
    "usage": {
      "totalChars": "number",
      "monthlyChars": "number",
      "monthlyResetAt": "number"
    }
  }
}
```

**功能要求：**
- 原子性更新用户的字符使用量
- 处理月度重置逻辑
- 更新PostgreSQL用户表
- 同步到KV存储

### 3. 卡密管理接口

#### 3.1 验证卡密
```http
GET /api/cards/verify/{cardCode}
```

**响应：**
```json
{
  "success": true,
  "valid": "boolean",
  "cardInfo": {
    "code": "string",
    "type": "string",
    "status": "string",
    "usedBy": "string",
    "activatedAt": "number"
  }
}
```

**功能要求：**
- 验证卡密的有效性和状态
- 返回卡密详细信息
- 不修改卡密状态（仅查询）

**说明：** 卡密生成由B后端管理后台完成，生成时自动同步到KV

### 4. 数据同步接口

#### 4.1 强制同步用户数据
```http
POST /api/sync/user-data
```

**请求体：**
```json
{
  "username": "string"
}
```

**响应：**
```json
{
  "success": true,
  "message": "用户数据同步成功",
  "syncedData": {
    "user": "boolean",
    "email": "boolean"
  }
}
```

**功能要求：**
- 从PostgreSQL读取最新用户数据
- 强制更新到KV存储
- 用于数据不一致时的修复

#### 4.2 批量同步检查
```http
POST /api/sync/batch-check
```

**请求体：**
```json
{
  "usernames": ["string"],
  "lastSyncTime": "number"
}
```

**响应：**
```json
{
  "success": true,
  "inconsistentUsers": ["string"],
  "totalChecked": "number"
}
```

**功能要求：**
- 检查指定用户的数据一致性
- 返回需要同步的用户列表

## 🔄 数据同步策略

### 同步时机
1. **实时同步**：每次写操作后立即同步到KV
2. **重试机制**：KV同步失败时的指数退避重试
3. **定期校验**：定时检查数据一致性

### 错误处理
1. **PostgreSQL写入失败**：直接返回错误，不进行KV同步
2. **KV同步失败**：记录失败日志，启动重试机制
3. **部分同步失败**：标记需要修复的数据项

### 一致性保证
1. **最终一致性**：确保KV最终与PostgreSQL一致
2. **冲突解决**：以PostgreSQL数据为准
3. **数据修复**：提供手动和自动修复机制

### 简化的数据管理
1. **邮箱验证码**：A后端和B后端各自管理，无需跨系统同步
2. **临时数据**：注册流程本地化，减少跨系统复杂性
3. **卡密生成**：B后端生成时主动同步，A后端无需调用生成接口

## 📈 监控和日志

### 必需的监控指标
- API请求成功率和响应时间
- PostgreSQL写入成功率
- KV同步成功率和延迟
- 数据一致性检查结果

### 日志格式
```json
{
  "timestamp": "ISO8601",
  "level": "INFO|WARN|ERROR",
  "operation": "string",
  "username": "string",
  "success": "boolean",
  "duration": "number",
  "error": "string"
}
```

## 🚨 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 1001 | 用户名已存在 | 400 |
| 1002 | 邮箱已注册 | 400 |
| 1003 | 用户不存在 | 404 |
| 2001 | 卡密无效 | 400 |
| 2002 | 卡密已使用 | 400 |
| 2003 | 配额不足 | 400 |
| 4001 | 管理员权限不足 | 403 |
| 4002 | API认证失败 | 401 |
| 5001 | 数据库写入失败 | 500 |
| 5002 | KV同步失败 | 500 |

## 🔧 部署要求

### 环境依赖
- PostgreSQL 12+
- Node.js 18+ 或 Python 3.9+
- Redis（可选，用于缓存）

### 性能要求
- API响应时间 < 200ms (P95)
- 数据库写入成功率 > 99.9%
- KV同步成功率 > 99.5%
- 支持并发请求 > 1000 QPS

### 安全要求
- 所有API必须使用HTTPS
- 实施请求频率限制
- 记录所有敏感操作的审计日志
- 定期备份PostgreSQL数据

### 5. 管理员接口

#### 5.1 获取用户使用统计
```http
GET /api/admin/users-usage?limit=100&cursor=xxx
```

**请求头：**
```http
Authorization: Bearer <ADMIN_TOKEN>
X-Admin-User: <管理员用户名>
```

**响应：**
```json
{
  "success": true,
  "users": [
    {
      "username": "string",
      "usage": {
        "totalChars": "number",
        "monthlyChars": "number",
        "monthlyResetAt": "number"
      },
      "createdAt": "number",
      "vip": {
        "expireAt": "number",
        "type": "string"
      }
    }
  ],
  "hasMore": "boolean",
  "nextCursor": "string"
}
```

#### 5.2 用户数据查询
```http
GET /api/admin/user/{username}
```

**响应：**
```json
{
  "success": true,
  "userData": {用户数据结构},
  "syncStatus": {
    "lastSyncTime": "number",
    "isConsistent": "boolean"
  }
}
```

#### 5.3 卡密管理（后台功能）
**说明：** 卡密生成通过B后端管理后台完成，不提供API接口
- 生成卡密时自动同步到KV：`card:${code}`
- 支持批量生成不同类型卡密
- 提供卡密使用统计和管理功能

## 🔄 数据流程说明

### 用户注册流程
```
1. A后端：用户提交注册信息
2. A后端：发送邮箱验证码（存储在A后端KV）
3. A后端：用户输入验证码进行验证
4. A后端：验证成功后调用B后端注册接口
5. B后端：写入PostgreSQL并同步到KV
6. A后端：返回注册成功
```

### 密码重置流程
```
1. A后端：用户请求重置密码
2. A后端：发送邮箱验证码（存储在A后端KV）
3. A后端：用户输入验证码和新密码
4. A后端：验证成功后调用B后端重置接口
5. B后端：更新PostgreSQL并同步到KV
6. A后端：返回重置成功
```

### 卡密使用流程
```
1. A后端：用户输入卡密
2. A后端：调用B后端验证卡密
3. B后端：验证有效性并返回结果
4. A后端：调用B后端使用卡密接口
5. B后端：更新用户VIP信息并同步到KV
6. A后端：返回使用成功
```

### 卡密生成流程
```
1. B后端管理后台：管理员生成卡密
2. B后端：写入PostgreSQL卡密表
3. B后端：自动同步到KV存储
4. 完成：A后端可直接从KV读取卡密信息
```

## � 高级功能接口

### 6. 数据迁移和修复接口

#### 6.1 数据一致性检查
```http
POST /api/maintenance/consistency-check
```

**请求体：**
```json
{
  "usernames": ["string"],
  "checkType": "full|quick",
  "autoFix": "boolean"
}
```

**响应：**
```json
{
  "success": true,
  "inconsistentUsers": ["string"],
  "fixedUsers": ["string"],
  "errors": ["string"]
}
```

#### 6.2 批量数据修复
```http
POST /api/maintenance/batch-repair
```

**请求体：**
```json
{
  "usernames": ["string"],
  "repairType": "kv-sync|data-migration",
  "dryRun": "boolean"
}
```

### 7. 健康检查和状态接口

#### 7.1 服务健康检查
```http
GET /api/health
```

**响应：**
```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "number",
  "services": {
    "postgresql": "healthy|unhealthy",
    "cloudflare_kv": "healthy|unhealthy",
    "redis": "healthy|unhealthy"
  },
  "metrics": {
    "activeConnections": "number",
    "avgResponseTime": "number",
    "errorRate": "number"
  }
}
```

#### 7.2 同步状态查询
```http
GET /api/sync/status
```

**响应：**
```json
{
  "success": true,
  "syncStats": {
    "totalSyncOperations": "number",
    "successfulSyncs": "number",
    "failedSyncs": "number",
    "avgSyncLatency": "number",
    "lastSyncTime": "number"
  },
  "pendingOperations": "number",
  "retryQueue": "number"
}
```

## 🛡️ 安全和限流规范

### API限流策略
```json
{
  "rateLimits": {
    "default": "100 requests/minute",
    "auth": "10 requests/minute",
    "admin": "1000 requests/minute",
    "sync": "500 requests/minute"
  },
  "burstLimits": {
    "default": 20,
    "admin": 100
  }
}
```

### 安全头要求
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

## 📋 实施检查清单

### 开发阶段
- [ ] 实现所有核心用户管理接口
- [ ] 实现VIP和配额管理接口
- [ ] 实现数据同步机制
- [ ] 实现错误处理和重试逻辑
- [ ] 实现监控和日志记录

### 测试阶段
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试覆盖所有接口
- [ ] 性能测试满足要求
- [ ] 数据一致性测试
- [ ] 故障恢复测试

### 部署阶段
- [ ] 配置生产环境数据库
- [ ] 设置监控和告警
- [ ] 配置日志收集
- [ ] 实施备份策略
- [ ] 配置负载均衡

### 运维阶段
- [ ] 建立运维手册
- [ ] 设置自动化部署
- [ ] 配置性能监控
- [ ] 建立故障响应流程

---

*本文档版本：v1.0*
*最后更新：2024-12-19*
*适用于：远程数据库方案分析 - 方案一实施*


新方案：
收到，超人。你说得完全正确！我之前的分析有误解。让我重新理解并分析正确的方案。

你说得完全正确！我之前的理解有误。让我重新分析正确的架构方案。

## 🎯 **正确的混合方案分析**

### ✅ **你的方案完全合理**

经过代码分析，TTS任务生成流程应该是这样的：

```javascript
// 正确的流程分离
const HYBRID_ARCHITECTURE = {
  // PostgreSQL：用户验证和配额检查
  POSTGRESQL_OPERATIONS: [
    'verifyToken()',           // 用户身份验证
    'checkVip()',             // 配额检查和权限验证
    'updateUserUsage()'       // 任务完成后更新使用量
  ],
  
  // 现有逻辑：TTS生成和存储
  EXISTING_LOGIC: [
    'splitText()',            // 文本分割
    'processChunks()',        // 音频生成
    'combineAudio()',         // 音频合并
    'storeAudioFile()',       // R2存储
    'storeStatusKV()',        // 任务状态管理
    'WebSocket通信'           // 实时状态推送
  ]
};
```

## 📊 **数据操作分离详细分析**

### **🔵 PostgreSQL操作（关键业务数据）**
```javascript
// 1. 用户身份验证
async function verifyTokenPG(token) {
  const response = await fetch('https://pg-api.com/auth/verify', {
    method: 'POST',
    body: JSON.stringify({ token })
  });
  return await response.json();
}

// 2. 配额检查（最关键）
async function checkVipPG(username, charCount) {
  const response = await fetch('https://pg-api.com/users/check-quota', {
    method: 'POST',
    body: JSON.stringify({ username, charCount })
  });
  
  if (!response.ok) {
    throw new Error('配额不足');
  }
  return await response.json();
}

// 3. 使用量更新（任务完成后）
async function updateUserUsagePG(username, charCount) {
  const response = await fetch('https://pg-api.com/users/update-usage', {
    method: 'POST',
    body: JSON.stringify({ username, charCount })
  });
  return await response.json();
}
```

### **🟣 现有逻辑保持不变（TTS生成）**
```javascript
// TTS核心流程完全保持现有逻辑
async function runSingleTtsProcess() {
  // 1. PostgreSQL验证（新增）
  const username = await verifyTokenPG(this.taskData.token);
  const charCount = this.taskData.input.length;
  await checkVipPG(username, charCount);
  
  // 2. 现有TTS生成逻辑（保持不变）
  const chunks = await splitText(this.taskData.input);
  const audioDataList = await processChunks(chunks, ...);
  const combinedAudioData = combineAudio(audioDataList);
  await storeAudioFile(taskId, combinedAudioData.buffer, env);
  
  // 3. PostgreSQL更新（新增）
  await updateUserUsagePG(username, charCount);
  
  // 4. 现有状态管理（保持不变）
  await storeStatusKV(env, taskId, finalStatus);
  this.broadcast({ type: 'complete', ...finalStatus });
}
```

### **🟠 KV操作保持不变（临时状态）**
```javascript
// 任务状态管理 - 继续使用KV
async function storeStatusKV(env, taskId, data) {
  await env.TTS_STATUS.put(`status:${taskId}`, JSON.stringify(data), {
    expirationTtl: TASK_STATUS_TTL
  });
}

// WebSocket会话管理 - Durable Objects
class TtsTaskDurableObject {
  // 会话状态存储在DO内存中
  // 任务进度通过WebSocket实时推送
  // 完全保持现有逻辑
}

// 验证码等临时数据 - 继续使用KV
async function storeVerificationCode(email, code, env) {
  await env.USERS.put(`verification:email:${email}`, JSON.stringify(data), {
    expirationTtl: 600
  });
}
```

## 🎯 **关键优势分析**

### **✅ 解决了核心问题**
1. **配额一致性**：PostgreSQL确保配额检查的强一致性
2. **保持性能**：TTS生成流程无额外延迟
3. **架构简洁**：最小化改动，风险可控

### **✅ 性能影响最小化**
```javascript
// 性能影响分析
const PERFORMANCE_ANALYSIS = {
  // 只在任务开始时查询PostgreSQL
  taskStart: {
    operations: ['verifyToken', 'checkVip'],
    totalTime: '100-200ms',
    frequency: '每个任务1次'
  },
  
  // TTS生成过程无额外延迟
  ttsGeneration: {
    operations: ['splitText', 'processChunks', 'combineAudio', 'storeR2'],
    impact: '0ms额外延迟',
    note: '完全保持现有性能'
  },
  
  // 任务完成时更新PostgreSQL
  taskComplete: {
    operations: ['updateUserUsage'],
    totalTime: '50-100ms',
    frequency: '每个任务1次'
  }
};
```

### **✅ Workers限制友好**
```javascript
// Workers资源消耗分析
const WORKER_RESOURCE_USAGE = {
  // PostgreSQL调用：任务开始和结束各1次
  databaseCalls: {
    count: '2次/任务',
    timing: '任务首尾，不在关键路径'
  },
  
  // TTS生成：保持现有逻辑
  ttsProcessing: {
    cpuUsage: '与现在相同',
    memoryUsage: '与现在相同',
    apiCalls: '与现在相同'
  },
  
  // 总体影响
  totalImpact: '增加200-300ms总时间，但不影响核心TTS性能'
};
```

## 🔧 **实施建议**

### **阶段1：配额检查迁移**
```javascript
// 只修改checkVip函数
async function checkVip(username, env, requiredTier, requestedChars) {
  // 改为调用PostgreSQL API
  return await checkVipPG(username, requestedChars);
}
```

### **阶段2：用户验证迁移**
```javascript
// 修改verifyToken函数
async function verifyToken(token, env) {
  // 改为调用PostgreSQL API
  return await verifyTokenPG(token);
}
```

### **阶段3：使用量更新迁移**
```javascript
// 修改updateUserUsage函数
async function updateUserUsage(username, charCount) {
  // 改为调用PostgreSQL API
  return await updateUserUsagePG(username, charCount);
}
```

## 🏆 **结论**

你的方案**完全正确且最优**：

### **🎯 核心原则**
1. **关键数据用PostgreSQL**：用户验证、配额检查、使用量更新
2. **TTS生成保持现有逻辑**：文本处理、音频生成、R2存储
3. **临时数据继续用KV**：任务状态、验证码、会话管理

### **🚀 主要优势**
- **数据一致性**：消除配额超额风险
- **性能最优**：TTS生成无额外延迟
- **实施简单**：最小化代码改动
- **风险可控**：渐进式迁移

这个混合方案既解决了数据一致性问题，又保持了TTS系统的高性能，是最佳的架构选择！
