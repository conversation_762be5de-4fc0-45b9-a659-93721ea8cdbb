#!/usr/bin/env node

/**
 * B后端用户同步接口测试脚本
 * 测试用户注册和密码重置接口
 */

require('dotenv').config();

const API_BASE_URL = 'http://localhost:3001';
const API_SECRET_TOKEN = process.env.API_SECRET_TOKEN;

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// API调用函数
async function callAPI(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_SECRET_TOKEN}`
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.text();
    
    let parsedResult;
    try {
      parsedResult = JSON.parse(result);
    } catch {
      parsedResult = result;
    }

    return {
      status: response.status,
      statusText: response.statusText,
      data: parsedResult,
      success: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      statusText: 'Network Error',
      data: { error: error.message },
      success: false
    };
  }
}

// 生成测试用户数据
function generateTestUser() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 8);
  
  return {
    username: `testuser_${randomId}`,
    passwordHash: `hash_${timestamp}_${randomId}`,
    email: `test_${randomId}@example.com`,
    createdAt: timestamp
  };
}

// 测试用户注册接口
async function testUserRegistration() {
  log('blue', '\n🧪 测试用户注册接口...');
  
  const testUser = generateTestUser();
  log('cyan', `测试用户: ${testUser.username} (${testUser.email})`);
  
  const response = await callAPI('/api/b-backend/users/register', 'POST', testUser);
  
  if (response.success) {
    log('green', '✅ 用户注册成功');
    log('green', `   用户名: ${response.data.userData.username}`);
    log('green', `   邮箱: ${response.data.userData.email}`);
    log('green', `   配额: ${response.data.userData.vip.quotaChars} 字符`);
    return testUser;
  } else {
    log('red', '❌ 用户注册失败');
    log('red', `   状态: ${response.status} ${response.statusText}`);
    log('red', `   错误: ${JSON.stringify(response.data, null, 2)}`);
    return null;
  }
}

// 测试密码重置接口
async function testPasswordReset(username) {
  log('blue', '\n🧪 测试密码重置接口...');
  
  const newPasswordHash = `new_hash_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  const resetData = {
    username,
    newPasswordHash,
    passwordUpdatedAt: Date.now()
  };
  
  log('cyan', `重置用户: ${username}`);
  
  const response = await callAPI('/api/b-backend/users/reset-password', 'POST', resetData);
  
  if (response.success) {
    log('green', '✅ 密码重置成功');
    log('green', `   消息: ${response.data.message}`);
    return true;
  } else {
    log('red', '❌ 密码重置失败');
    log('red', `   状态: ${response.status} ${response.statusText}`);
    log('red', `   错误: ${JSON.stringify(response.data, null, 2)}`);
    return false;
  }
}

// 测试重复注册（应该失败）
async function testDuplicateRegistration(existingUser) {
  log('blue', '\n🧪 测试重复注册（应该失败）...');
  
  const response = await callAPI('/api/b-backend/users/register', 'POST', existingUser);
  
  if (!response.success && response.status === 409) {
    log('green', '✅ 重复注册正确被拒绝');
    log('green', `   错误码: ${response.data.code}`);
    log('green', `   错误信息: ${response.data.error}`);
    return true;
  } else {
    log('red', '❌ 重复注册应该被拒绝但没有');
    log('red', `   状态: ${response.status} ${response.statusText}`);
    log('red', `   响应: ${JSON.stringify(response.data, null, 2)}`);
    return false;
  }
}

// 测试无效参数
async function testInvalidParameters() {
  log('blue', '\n🧪 测试无效参数...');
  
  const invalidCases = [
    {
      name: '缺少用户名',
      data: { passwordHash: 'test_hash', email: '<EMAIL>' }
    },
    {
      name: '缺少密码哈希',
      data: { username: 'testuser', email: '<EMAIL>' }
    },
    {
      name: '无效用户名格式',
      data: { username: 'ab', passwordHash: 'test_hash' }
    },
    {
      name: '无效邮箱格式',
      data: { username: 'testuser123', passwordHash: 'test_hash', email: 'invalid-email' }
    }
  ];
  
  let passedTests = 0;
  
  for (const testCase of invalidCases) {
    log('cyan', `  测试: ${testCase.name}`);
    
    const response = await callAPI('/api/b-backend/users/register', 'POST', testCase.data);
    
    if (!response.success && response.status === 400) {
      log('green', `    ✅ 正确拒绝: ${response.data.error}`);
      passedTests++;
    } else {
      log('red', `    ❌ 应该被拒绝但没有: ${response.status} ${JSON.stringify(response.data)}`);
    }
  }
  
  return passedTests === invalidCases.length;
}

// 测试不存在用户的密码重置
async function testNonExistentUserPasswordReset() {
  log('blue', '\n🧪 测试不存在用户的密码重置...');
  
  const resetData = {
    username: 'nonexist_user123',
    newPasswordHash: 'new_hash_test',
    passwordUpdatedAt: Date.now()
  };
  
  const response = await callAPI('/api/b-backend/users/reset-password', 'POST', resetData);
  
  if (!response.success && response.status === 404) {
    log('green', '✅ 不存在用户的密码重置正确被拒绝');
    log('green', `   错误码: ${response.data.code}`);
    log('green', `   错误信息: ${response.data.error}`);
    return true;
  } else {
    log('red', '❌ 不存在用户的密码重置应该被拒绝但没有');
    log('red', `   状态: ${response.status} ${response.statusText}`);
    log('red', `   响应: ${JSON.stringify(response.data, null, 2)}`);
    return false;
  }
}

// 主测试函数
async function runTests() {
  log('blue', '🚀 开始B后端用户同步接口测试...\n');
  
  if (!API_SECRET_TOKEN) {
    log('red', '❌ 缺少API_SECRET_TOKEN环境变量');
    process.exit(1);
  }
  
  const results = {
    userRegistration: false,
    passwordReset: false,
    duplicateRegistration: false,
    invalidParameters: false,
    nonExistentUserReset: false
  };
  
  try {
    // 1. 测试用户注册
    const testUser = await testUserRegistration();
    results.userRegistration = !!testUser;
    
    if (testUser) {
      // 2. 测试密码重置
      results.passwordReset = await testPasswordReset(testUser.username);
      
      // 3. 测试重复注册
      results.duplicateRegistration = await testDuplicateRegistration(testUser);
    }
    
    // 4. 测试无效参数
    results.invalidParameters = await testInvalidParameters();
    
    // 5. 测试不存在用户的密码重置
    results.nonExistentUserReset = await testNonExistentUserPasswordReset();
    
  } catch (error) {
    log('red', `❌ 测试过程中发生错误: ${error.message}`);
  }
  
  // 输出测试结果
  log('blue', '\n📊 测试结果汇总:');
  log('blue', '================================');
  
  const testNames = {
    userRegistration: '用户注册',
    passwordReset: '密码重置',
    duplicateRegistration: '重复注册拒绝',
    invalidParameters: '无效参数拒绝',
    nonExistentUserReset: '不存在用户重置拒绝'
  };
  
  let passedCount = 0;
  let totalCount = 0;
  
  for (const [key, passed] of Object.entries(results)) {
    totalCount++;
    if (passed) {
      passedCount++;
      log('green', `✅ ${testNames[key]}: 通过`);
    } else {
      log('red', `❌ ${testNames[key]}: 失败`);
    }
  }
  
  log('blue', '================================');
  log(passedCount === totalCount ? 'green' : 'yellow', 
      `📈 测试通过率: ${passedCount}/${totalCount} (${(passedCount/totalCount*100).toFixed(1)}%)`);
  
  if (passedCount === totalCount) {
    log('green', '🎉 所有测试通过！用户同步接口工作正常');
  } else {
    log('yellow', '⚠️  部分测试失败，请检查接口实现');
  }
}

// 执行测试
if (require.main === module) {
  runTests().catch(error => {
    log('red', `测试执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests };
