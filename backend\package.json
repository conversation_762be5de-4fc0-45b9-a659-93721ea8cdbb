{"name": "tts-app-server", "version": "1.0.0", "description": "Text-to-Speech API服务器 - 从Cloudflare Worker迁移到Ubuntu服务器", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "env:dev": "cp .env.development .env && echo 已切换到开发环境", "env:prod": "cp .env.production .env && echo 已切换到生产环境", "start:dev": "npm run env:dev && node src/app.js", "start:prod": "npm run env:prod && node src/app.js", "dev:auto": "npm run env:dev && nodemon src/app.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:dev": "npm run env:dev && pm2 start ecosystem.config.js --env development", "pm2:prod": "npm run env:prod && pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate:create": "node scripts/create_tables.js", "migrate:data": "node scripts/migrate_data.js", "setup": "npm run migrate:create && npm run migrate:data", "cards:create": "node scripts/create-test-cards.js", "cards:test": "node test-card-activation.js", "user:create": "node create-test-user.js", "vip:test": "node test-vip-system.js"}, "keywords": ["tts", "text-to-speech", "elevenlabs", "api", "nodejs", "express", "websocket"], "author": "TTS Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-ws": "^5.0.2", "fetch-socks": "^1.3.2", "ioredis": "^5.3.2", "p-limit": "^3.1.0", "pg": "^8.11.3", "socks-proxy-agent": "^8.0.5", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/tts-app-server.git"}, "bugs": {"url": "https://github.com/your-org/tts-app-server/issues"}, "homepage": "https://github.com/your-org/tts-app-server#readme"}